@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Quản lý đơn đặt vé";
    var totalTickets = Model.Ves?.Count ?? 0;
    var bookedTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat) ?? 0;
    var paidTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaThanhToan) ?? 0;
    var usedTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaSuDung) ?? 0;
    var completedTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0;
    var cancelledTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0;
    var refundedTickets = Model.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanTien) ?? 0;
    
    var isUpcoming = Model.NgayKhoiHanh > DateTime.Now;
    var statusClass = isUpcoming ? "bg-success" : "bg-secondary";
    var statusText = isUpcoming ? "Sắp khởi hành" : "Đã khởi hành";
    
    if (Model.TrangThai == false)
    {
        statusClass = "bg-danger";
        statusText = "Đã hủy";
    }
}

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Admin" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-controller="BookingManagement" asp-action="Index">Quản lý đặt vé</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn đặt vé</li>
                </ol>
            </nav>
            <h2 class="mb-0"><i class="bi bi-ticket-detailed me-2"></i>Chi tiết đơn đặt vé</h2>
        </div>
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
            @if (totalTickets > 0)
            {
                <a asp-action="ExportPassengerList" asp-route-id="@Model.ChuyenXeId" class="btn btn-admin-success ms-2">
                    <i class="bi bi-file-earmark-excel me-1"></i>Xuất danh sách
                </a>
            }
        </div>
    </div>

    <!-- Thông tin chuyến xe -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card admin-card mb-4">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Thông tin chuyến xe</h5>
                    <span class="badge @statusClass">@statusText</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150" class="ps-0">Tuyến đường:</th>
                                    <td class="fw-semibold">@Model.DiemDiDisplay → @Model.DiemDenDisplay</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Ngày khởi hành:</th>
                                    <td>@Model.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Quãng đường:</th>
                                    <td>@Model.TuyenDuong?.KhoangCach km</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Thời gian:</th>
                                    <td>@Model.TuyenDuong?.ThoiGianDuKien.ToString(@"hh\:mm") (dự kiến)</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="150" class="ps-0">Xe:</th>
                                    <td>@Model.Xe?.BienSo (@Model.Xe?.LoaiXe)</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Tài xế:</th>
                                    <td>@(Model.TaiXe?.HoTen ?? "Chưa phân công")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Số điện thoại:</th>
                                    <td>@(Model.TaiXe?.SoDienThoai ?? "N/A")</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">Giá vé:</th>
                                    <td>@Model.Gia.ToString("#,##0") VNĐ</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card admin-card">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Trạng thái đặt chỗ</h5>
                </div>
                <div class="card-body">
                    @{ 
                        var totalSeats = Model.Xe?.SoGhe ?? 0;
                        var bookedSeats = totalTickets;
                        var availableSeats = totalSeats - bookedSeats;
                        var percentBooked = totalSeats > 0 ? (int)((double)bookedSeats / totalSeats * 100) : 0;
                    }
                    
                    <div class="text-center mb-3">
                        <div class="fs-1 fw-bold">@bookedSeats/@totalSeats</div>
                        <div class="text-muted">Ghế đã đặt</div>
                    </div>
                    
                    <div class="progress mb-3" style="height: 15px;">
                        <div class="progress-bar @(percentBooked > 80 ? "bg-danger" : percentBooked > 50 ? "bg-warning" : "bg-success")" 
                             role="progressbar" 
                             style="width: @percentBooked%" 
                             aria-valuenow="@percentBooked" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            @percentBooked%
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between text-muted small">
                        <div><span class="fw-medium text-dark">@bookedSeats</span> ghế đã đặt</div>
                        <div><span class="fw-medium text-dark">@availableSeats</span> ghế trống</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê vé -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle mx-auto mb-3">
                        <i class="bi bi-ticket-perforated fs-4"></i>
                    </div>
                    <h3 class="mb-0">@bookedTickets</h3>
                    <div class="text-muted small">Đã đặt</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-success bg-opacity-10 text-success rounded-circle mx-auto mb-3">
                        <i class="bi bi-cash-coin fs-4"></i>
                    </div>
                    <h3 class="mb-0">@paidTickets</h3>
                    <div class="text-muted small">Đã thanh toán</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-info bg-opacity-10 text-info rounded-circle mx-auto mb-3">
                        <i class="bi bi-person-check fs-4"></i>
                    </div>
                    <h3 class="mb-0">@usedTickets</h3>
                    <div class="text-muted small">Đã sử dụng</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle mx-auto mb-3">
                        <i class="bi bi-check-circle fs-4"></i>
                    </div>
                    <h3 class="mb-0">@completedTickets</h3>
                    <div class="text-muted small">Đã hoàn thành</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-danger bg-opacity-10 text-danger rounded-circle mx-auto mb-3">
                        <i class="bi bi-x-circle fs-4"></i>
                    </div>
                    <h3 class="mb-0">@cancelledTickets</h3>
                    <div class="text-muted small">Đã hủy</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card admin-card h-100">
                <div class="card-body text-center">
                    <div class="icon-box bg-warning bg-opacity-10 text-warning rounded-circle mx-auto mb-3">
                        <i class="bi bi-arrow-return-left fs-4"></i>
                    </div>
                    <h3 class="mb-0">@refundedTickets</h3>
                    <div class="text-muted small">Đã hoàn tiền</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách vé -->
    <div class="card admin-card">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>Danh sách vé (@totalTickets)</h5>
            @if (totalTickets > 0)
            {
                <div class="d-flex gap-2 align-items-center">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAllTickets">
                        <label class="form-check-label" for="selectAllTickets">Chọn tất cả</label>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="bulkActionDropdown" data-bs-toggle="dropdown" aria-expanded="false" disabled>
                            <i class="bi bi-gear me-1"></i>Thao tác hàng loạt
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="bulkActionDropdown">
                            <li><a class="dropdown-item bulk-update" data-status="@((int)TrangThaiVe.DaThanhToan)" href="#"><i class="bi bi-cash-coin me-2"></i>Đã thanh toán</a></li>
                            <li><a class="dropdown-item bulk-update" data-status="@((int)TrangThaiVe.DaSuDung)" href="#"><i class="bi bi-person-check me-2"></i>Đã sử dụng</a></li>
                            <li><a class="dropdown-item bulk-update" data-status="@((int)TrangThaiVe.DaHoanThanh)" href="#"><i class="bi bi-check-circle me-2"></i>Đã hoàn thành</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item bulk-update text-danger" data-status="@((int)TrangThaiVe.DaHuy)" href="#"><i class="bi bi-x-circle me-2"></i>Đã hủy</a></li>
                            <li><a class="dropdown-item bulk-update" data-status="@((int)TrangThaiVe.DaHoanTien)" href="#"><i class="bi bi-arrow-return-left me-2"></i>Đã hoàn tiền</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="btnSendNotification"><i class="bi bi-envelope me-2"></i>Gửi thông báo</a></li>
                        </ul>
                    </div>
                    <div class="ms-2">
                        <input type="text" class="form-control" id="searchTickets" placeholder="Tìm kiếm vé..." style="min-width: 200px;">
                    </div>
                </div>
            }
        </div>
        <div class="card-body p-0">
            @if (totalTickets > 0)
            {
                <div class="table-responsive">
                    <table class="table table-hover align-middle admin-table mb-0" id="ticketsTable">
                        <thead>
                            <tr>
                                <th width="40px" class="text-center">
                                    <div class="form-check d-flex justify-content-center">
                                        <input class="form-check-input" type="checkbox" id="selectAllTicketsHeader">
                                    </div>
                                </th>
                                <th>Mã vé</th>
                                <th>Thông tin khách</th>
                                <th>Ghế</th>
                                <th>Ngày đặt</th>
                                <th>Trạng thái</th>
                                <th>Ghi chú</th>
                                <th width="120px" class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var ve in (Model.Ves != null ? Model.Ves.OrderBy(v => v.ChoNgoi?.SoGhe) : Enumerable.Empty<Ve>()))
                            {
                                var ticketStatusClass = "";
                                var ticketStatusText = "";
                                
                                switch (ve.TrangThai)
                                {
                                    case TrangThaiVe.DaDat:
                                        ticketStatusClass = "bg-primary";
                                        ticketStatusText = "Đã đặt";
                                        break;
                                    case TrangThaiVe.DaThanhToan:
                                        ticketStatusClass = "bg-success";
                                        ticketStatusText = "Đã thanh toán";
                                        break;
                                    case TrangThaiVe.DaSuDung:
                                        ticketStatusClass = "bg-info";
                                        ticketStatusText = "Đã sử dụng";
                                        break;
                                    case TrangThaiVe.DaHoanThanh:
                                        ticketStatusClass = "bg-primary";
                                        ticketStatusText = "Đã hoàn thành";
                                        break;
                                    case TrangThaiVe.DaHuy:
                                        ticketStatusClass = "bg-danger";
                                        ticketStatusText = "Đã hủy";
                                        break;
                                    case TrangThaiVe.DaHoanTien:
                                        ticketStatusClass = "bg-warning";
                                        ticketStatusText = "Đã hoàn tiền";
                                        break;
                                }

                                <tr data-search="@ve.MaVe @ve.TenKhach @ve.SoDienThoai @ve.Email @ve.ChoNgoi?.SoGhe">
                                    <td class="text-center">
                                        <div class="form-check d-flex justify-content-center">
                                            <input class="form-check-input ticket-checkbox" type="checkbox" value="@ve.VeId" id="<EMAIL>">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">@ve.MaVe</div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">@ve.TenKhach</div>
                                        <div class="text-muted small">
                                            <i class="bi bi-telephone me-1"></i>@ve.SoDienThoai
                                        </div>
                                        <div class="text-muted small">
                                            <i class="bi bi-envelope me-1"></i>@ve.Email
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-person-fill me-1"></i>Ghế @ve.ChoNgoi?.SoGhe
                                        </span>
                                    </td>
                                    <td>
                                        <div>@ve.NgayDat.ToString("dd/MM/yyyy")</div>
                                        <div class="text-muted small">@ve.NgayDat.ToString("HH:mm")</div>
                                    </td>
                                    <td>
                                        <span class="badge admin-badge @ticketStatusClass">@ticketStatusText</span>
                                        @if (ve.TrangThai == TrangThaiVe.DaHuy && !string.IsNullOrEmpty(ve.LyDoHuy))
                                        {
                                            <div class="text-muted small mt-1">
                                                <i class="bi bi-info-circle me-1"></i>Lý do: @ve.LyDoHuy
                                            </div>
                                        }
                                    </td>
                                    <td>
                                        <div class="text-muted small">@(string.IsNullOrEmpty(ve.GhiChu) ? "Không có" : ve.GhiChu)</div>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                Cập nhật
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item update-status" data-id="@ve.VeId" data-status="@((int)TrangThaiVe.DaThanhToan)" href="#"><i class="bi bi-cash-coin me-2"></i>Đã thanh toán</a></li>
                                                <li><a class="dropdown-item update-status" data-id="@ve.VeId" data-status="@((int)TrangThaiVe.DaSuDung)" href="#"><i class="bi bi-person-check me-2"></i>Đã sử dụng</a></li>
                                                <li><a class="dropdown-item update-status" data-id="@ve.VeId" data-status="@((int)TrangThaiVe.DaHoanThanh)" href="#"><i class="bi bi-check-circle me-2"></i>Đã hoàn thành</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item update-status text-danger" data-id="@ve.VeId" data-status="@((int)TrangThaiVe.DaHuy)" href="#"><i class="bi bi-x-circle me-2"></i>Đã hủy</a></li>
                                                <li><a class="dropdown-item update-status" data-id="@ve.VeId" data-status="@((int)TrangThaiVe.DaHoanTien)" href="#"><i class="bi bi-arrow-return-left me-2"></i>Đã hoàn tiền</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item add-note" data-id="@ve.VeId" data-note="@ve.GhiChu" href="#"><i class="bi bi-pencil me-2"></i>Thêm ghi chú</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-ticket-perforated display-1 text-muted"></i>
                    <p class="mt-3 text-muted">Chưa có vé nào được đặt cho chuyến xe này</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal xác nhận hủy vé -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận hủy vé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="cancelReason" class="form-label">Lý do hủy <span class="text-danger">*</span></label>
                    <textarea id="cancelReason" class="form-control" rows="3" required></textarea>
                </div>
                <input type="hidden" id="cancelTicketId" value="">
                <input type="hidden" id="isBulkCancel" value="false">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-danger" id="confirmCancel">Xác nhận hủy</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal thêm ghi chú -->
<div class="modal fade" id="noteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm ghi chú</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="ticketNote" class="form-label">Ghi chú</label>
                    <textarea id="ticketNote" class="form-control" rows="3"></textarea>
                </div>
                <input type="hidden" id="noteTicketId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="saveNote">Lưu ghi chú</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gửi thông báo cho hành khách</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>Thông báo sẽ được gửi qua email đến tất cả hành khách đã chọn.
                </div>
                
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                    <input type="text" id="notificationSubject" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label">Nội dung <span class="text-danger">*</span></label>
                    <textarea id="notificationMessage" class="form-control" rows="5" required></textarea>
                </div>
                
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="sendSMS">
                    <label class="form-check-label" for="sendSMS">Đồng thời gửi tin nhắn SMS</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="sendNotification">
                    <i class="bi bi-envelope me-1"></i>Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking-management').addClass('active');
            
            // Xử lý tìm kiếm
            $('#searchTickets').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $("#ticketsTable tbody tr").filter(function() {
                    var searchData = $(this).data('search').toLowerCase();
                    $(this).toggle(searchData.indexOf(value) > -1);
                });
            });
            
            // Xử lý chọn tất cả vé
            $('#selectAllTickets, #selectAllTicketsHeader').change(function() {
                $('.ticket-checkbox').prop('checked', $(this).is(':checked'));
                updateBulkActionButton();
                
                // Đồng bộ giữa 2 checkbox "Chọn tất cả"
                if (this.id === 'selectAllTickets') {
                    $('#selectAllTicketsHeader').prop('checked', $(this).is(':checked'));
                } else {
                    $('#selectAllTickets').prop('checked', $(this).is(':checked'));
                }
            });
            
            // Xử lý khi chọn/bỏ chọn từng vé
            $('.ticket-checkbox').change(function() {
                updateBulkActionButton();
                
                // Nếu tất cả các checkbox đều được chọn, chọn checkbox "Chọn tất cả"
                if ($('.ticket-checkbox:checked').length === $('.ticket-checkbox').length) {
                    $('#selectAllTickets, #selectAllTicketsHeader').prop('checked', true);
                } else {
                    $('#selectAllTickets, #selectAllTicketsHeader').prop('checked', false);
                }
            });
            
            // Cập nhật trạng thái nút "Cập nhật hàng loạt"
            function updateBulkActionButton() {
                if ($('.ticket-checkbox:checked').length > 0) {
                    $('#bulkActionDropdown').prop('disabled', false);
                } else {
                    $('#bulkActionDropdown').prop('disabled', true);
                }
            }
            
            // Xử lý cập nhật trạng thái một vé
            $('.update-status').click(function(e) {
                e.preventDefault();
                var ticketId = $(this).data('id');
                var status = $(this).data('status');
                
                // Nếu trạng thái là "Đã hủy", hiển thị modal xác nhận
                if (status == @((int)TrangThaiVe.DaHuy)) {
                    $('#cancelTicketId').val(ticketId);
                    $('#isBulkCancel').val('false');
                    $('#cancelReason').val('');
                    $('#cancelModal').modal('show');
                } else {
                    updateTicketStatus(ticketId, status);
                }
            });
            
            // Xử lý cập nhật hàng loạt
            $('.bulk-update').click(function(e) {
                e.preventDefault();
                var status = $(this).data('status');
                var selectedTickets = [];
                
                $('.ticket-checkbox:checked').each(function() {
                    selectedTickets.push($(this).val());
                });
                
                if (selectedTickets.length === 0) {
                    return;
                }
                
                // Nếu trạng thái là "Đã hủy", hiển thị modal xác nhận
                if (status == @((int)TrangThaiVe.DaHuy)) {
                    $('#cancelTicketId').val(selectedTickets.join(','));
                    $('#isBulkCancel').val('true');
                    $('#cancelReason').val('');
                    $('#cancelModal').modal('show');
                } else {
                    updateMultipleTickets(selectedTickets, status);
                }
            });
            
            // Xử lý xác nhận hủy vé
            $('#confirmCancel').click(function() {
                var reason = $('#cancelReason').val();
                
                if (!reason) {
                    showToast('Vui lòng nhập lý do hủy vé', 'danger');
                    return;
                }
                
                var isBulk = $('#isBulkCancel').val() === 'true';
                var ticketId = $('#cancelTicketId').val();
                
                if (isBulk) {
                    var selectedTickets = ticketId.split(',');
                    updateMultipleTickets(selectedTickets, @((int)TrangThaiVe.DaHuy), reason);
                } else {
                    updateTicketStatus(ticketId, @((int)TrangThaiVe.DaHuy), reason);
                }
                
                $('#cancelModal').modal('hide');
            });
            
            // Xử lý thêm ghi chú
            $('.add-note').click(function(e) {
                e.preventDefault();
                var ticketId = $(this).data('id');
                var note = $(this).data('note') || '';
                
                $('#noteTicketId').val(ticketId);
                $('#ticketNote').val(note);
                $('#noteModal').modal('show');
            });
            
            // Xử lý lưu ghi chú
            $('#saveNote').click(function() {
                var ticketId = $('#noteTicketId').val();
                var note = $('#ticketNote').val();
                
                $.ajax({
                    url: '@Url.Action("UpdateTicketStatus")',
                    type: 'POST',
                    data: {
                        veId: ticketId,
                        trangThai: 0, // Không đổi trạng thái
                        ghiChu: note
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã cập nhật ghi chú', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật ghi chú', 'danger');
                    }
                });
                
                $('#noteModal').modal('hide');
            });
            
            // Xử lý hiển thị modal gửi thông báo
            $('#btnSendNotification').click(function(e) {
                e.preventDefault();
                
                var selectedTickets = [];
                $('.ticket-checkbox:checked').each(function() {
                    selectedTickets.push($(this).val());
                });
                
                if (selectedTickets.length === 0) {
                    showToast('Vui lòng chọn ít nhất một vé', 'warning');
                    return;
                }
                
                // Tự động điền tiêu đề với thông tin chuyến xe
                $('#notificationSubject').val('Thông báo chuyến xe @Model.DiemDiDisplay - @Model.DiemDenDisplay ngày @Model.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")');
                $('#notificationMessage').val('');
                $('#notificationModal').modal('show');
            });
            
            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();
                var sendSMS = $('#sendSMS').is(':checked');
                
                if (!subject || !message) {
                    showToast('Vui lòng nhập đầy đủ tiêu đề và nội dung', 'warning');
                    return;
                }
                
                var selectedTickets = [];
                $('.ticket-checkbox:checked').each(function() {
                    selectedTickets.push($(this).val());
                });
                
                $.ajax({
                    url: '@Url.Action("SendNotification")',
                    type: 'POST',
                    data: {
                        veIds: selectedTickets,
                        subject: subject,
                        message: message
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    }
                });
                
                $('#notificationModal').modal('hide');
            });
            
            // Hàm cập nhật trạng thái một vé
            function updateTicketStatus(ticketId, status, reason = '') {
                $.ajax({
                    url: '@Url.Action("UpdateTicketStatus")',
                    type: 'POST',
                    data: {
                        veId: ticketId,
                        trangThai: status,
                        ghiChu: reason
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật trạng thái vé', 'danger');
                    }
                });
            }
            
            // Hàm cập nhật trạng thái nhiều vé
            function updateMultipleTickets(ticketIds, status, reason = '') {
                $.ajax({
                    url: '@Url.Action("UpdateMultipleTickets")',
                    type: 'POST',
                    data: {
                        veIds: ticketIds,
                        trangThai: status,
                        ghiChu: reason
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật trạng thái vé', 'danger');
                    }
                });
            }
            
            // Hiển thị thông báo toast
            function showToast(message, type) {
                var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
                toast.html(
                    '<div class="d-flex">'+
                    '  <div class="toast-body">' + message + '</div>'+
                    '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                    '</div>'
                );
                
                $('.toast-container').append(toast);
                var bsToast = new bootstrap.Toast(toast, { delay: 3000 });
                bsToast.show();
                
                // Xóa toast sau khi ẩn
                toast.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }
        });
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
}

@section Styles {
    <style>
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .table th {
            font-weight: 600;
        }
        .badge {
            font-size: 0.75rem;
        }
    </style>
}
