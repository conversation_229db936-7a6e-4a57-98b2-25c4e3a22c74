using DatVeXe.Models;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

namespace DatVeXe.Services
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null)
        {
            try
            {
                // Demo mode - Log email content instead of sending
                _logger.LogInformation($"[DEMO MODE] Email would be sent to: {toEmail}");
                _logger.LogInformation($"[DEMO MODE] Subject: {subject}");
                _logger.LogInformation($"[DEMO MODE] Content: {plainTextBody ?? htmlBody}");

                // Simulate email sending delay
                await Task.Delay(100);

                _logger.LogInformation($"[DEMO MODE] Email sent successfully to {toEmail}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email to {toEmail}");
                return false;
            }
        }

        public async Task<bool> SendTicketConfirmationAsync(string toEmail, string customerName, string ticketCode, 
            string tripInfo, string seatInfo, decimal price, DateTime departureTime)
        {
            var subject = $"Xác nhận đặt vé thành công - Mã vé: {ticketCode}";
            var htmlBody = GenerateTicketConfirmationHtml(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);
            var plainTextBody = GenerateTicketConfirmationText(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);

            return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
        }

        public async Task<bool> SendTicketCancellationAsync(string toEmail, string customerName, string ticketCode, 
            string tripInfo, string reason)
        {
            var subject = $"Thông báo hủy vé - Mã vé: {ticketCode}";
            var htmlBody = GenerateTicketCancellationHtml(customerName, ticketCode, tripInfo, reason);
            var plainTextBody = GenerateTicketCancellationText(customerName, ticketCode, tripInfo, reason);

            return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
        }

        private string GenerateTicketConfirmationHtml(string customerName, string ticketCode, string tripInfo, 
            string seatInfo, decimal price, DateTime departureTime)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Xác nhận đặt vé</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
        .ticket-info {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .info-row {{ display: flex; justify-content: space-between; margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #eee; }}
        .label {{ font-weight: bold; color: #555; }}
        .value {{ color: #333; }}
        .price {{ font-size: 24px; font-weight: bold; color: #e74c3c; text-align: center; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
        .logo {{ font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
        .qr-note {{ background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>🚌 Hệ thống đặt vé xe</div>
            <h2>Xác nhận đặt vé thành công!</h2>
        </div>
        
        <div class='content'>
            <p>Xin chào <strong>{customerName}</strong>,</p>
            <p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi. Vé của bạn đã được đặt thành công!</p>
            
            <div class='ticket-info'>
                <h3 style='color: #667eea; margin-top: 0;'>🎫 Thông tin vé</h3>
                <div class='info-row'>
                    <span class='label'>Mã vé:</span>
                    <span class='value'><strong>{ticketCode}</strong></span>
                </div>
                <div class='info-row'>
                    <span class='label'>Tuyến đường:</span>
                    <span class='value'>{tripInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Chỗ ngồi:</span>
                    <span class='value'>{seatInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Thời gian khởi hành:</span>
                    <span class='value'>{departureTime:dd/MM/yyyy HH:mm}</span>
                </div>
                <div class='price'>
                    💰 Tổng tiền: {price:N0} VNĐ
                </div>
            </div>
            
            <div class='qr-note'>
                <p><strong>📱 Lưu ý quan trọng:</strong></p>
                <p>Vui lòng mang theo email này hoặc mã vé <strong>{ticketCode}</strong> khi lên xe.</p>
                <p>Có mặt tại bến xe trước giờ khởi hành ít nhất 15 phút.</p>
            </div>
            
            <p>Nếu bạn có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi qua:</p>
            <ul>
                <li>📞 Hotline: 1900-xxxx</li>
                <li>📧 Email: <EMAIL></li>
                <li>🌐 Website: www.datvexe.com</li>
            </ul>
        </div>
        
        <div class='footer'>
            <p>Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
            <p><em>Email này được gửi tự động, vui lòng không trả lời.</em></p>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateTicketConfirmationText(string customerName, string ticketCode, string tripInfo, 
            string seatInfo, decimal price, DateTime departureTime)
        {
            return $@"
XÁC NHẬN ĐẶT VÉ THÀNH CÔNG

Xin chào {customerName},

Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi. Vé của bạn đã được đặt thành công!

THÔNG TIN VÉ:
- Mã vé: {ticketCode}
- Tuyến đường: {tripInfo}
- Chỗ ngồi: {seatInfo}
- Thời gian khởi hành: {departureTime:dd/MM/yyyy HH:mm}
- Tổng tiền: {price:N0} VNĐ

LƯU Ý QUAN TRỌNG:
- Vui lòng mang theo email này hoặc mã vé {ticketCode} khi lên xe
- Có mặt tại bến xe trước giờ khởi hành ít nhất 15 phút

LIÊN HỆ HỖ TRỢ:
- Hotline: 1900-xxxx
- Email: <EMAIL>
- Website: www.datvexe.com

Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!

---
Email này được gửi tự động, vui lòng không trả lời.
";
        }

        private string GenerateTicketCancellationHtml(string customerName, string ticketCode, string tripInfo, string reason)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Thông báo hủy vé</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
        .ticket-info {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .info-row {{ display: flex; justify-content: space-between; margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #eee; }}
        .label {{ font-weight: bold; color: #555; }}
        .value {{ color: #333; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
        .logo {{ font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>🚌 Hệ thống đặt vé xe</div>
            <h2>Thông báo hủy vé</h2>
        </div>
        
        <div class='content'>
            <p>Xin chào <strong>{customerName}</strong>,</p>
            <p>Chúng tôi xin thông báo vé của bạn đã được hủy.</p>
            
            <div class='ticket-info'>
                <h3 style='color: #e74c3c; margin-top: 0;'>🎫 Thông tin vé đã hủy</h3>
                <div class='info-row'>
                    <span class='label'>Mã vé:</span>
                    <span class='value'><strong>{ticketCode}</strong></span>
                </div>
                <div class='info-row'>
                    <span class='label'>Tuyến đường:</span>
                    <span class='value'>{tripInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Lý do hủy:</span>
                    <span class='value'>{reason}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Thời gian hủy:</span>
                    <span class='value'>{DateTime.Now:dd/MM/yyyy HH:mm}</span>
                </div>
            </div>
            
            <div class='warning'>
                <p><strong>⚠️ Lưu ý:</strong></p>
                <p>Nếu bạn đã thanh toán, chúng tôi sẽ tiến hành hoàn tiền theo chính sách của công ty.</p>
                <p>Thời gian hoàn tiền: 3-7 ngày làm việc.</p>
            </div>
            
            <p>Nếu bạn có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi qua:</p>
            <ul>
                <li>📞 Hotline: 1900-xxxx</li>
                <li>📧 Email: <EMAIL></li>
                <li>🌐 Website: www.datvexe.com</li>
            </ul>
        </div>
        
        <div class='footer'>
            <p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>
            <p><em>Email này được gửi tự động, vui lòng không trả lời.</em></p>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateTicketCancellationText(string customerName, string ticketCode, string tripInfo, string reason)
        {
            return $@"
THÔNG BÁO HỦY VÉ

Xin chào {customerName},

Chúng tôi xin thông báo vé của bạn đã được hủy.

THÔNG TIN VÉ ĐÃ HỦY:
- Mã vé: {ticketCode}
- Tuyến đường: {tripInfo}
- Lý do hủy: {reason}
- Thời gian hủy: {DateTime.Now:dd/MM/yyyy HH:mm}

LƯU Ý:
- Nếu bạn đã thanh toán, chúng tôi sẽ tiến hành hoàn tiền theo chính sách của công ty
- Thời gian hoàn tiền: 3-7 ngày làm việc

LIÊN HỆ HỖ TRỢ:
- Hotline: 1900-xxxx
- Email: <EMAIL>
- Website: www.datvexe.com

Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!

---
Email này được gửi tự động, vui lòng không trả lời.
";
        }
    }
}
