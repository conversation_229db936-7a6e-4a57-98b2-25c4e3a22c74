@model IEnumerable<dynamic>
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Quản lý đơn đặt vé";
    var chuyenXeList = ViewBag.ChuyenXeList as List<DatVeXe.Models.ChuyenXe>;
    var trangThaiVeList = ViewBag.TrangThaiVeList as List<DatVeXe.Models.TrangThaiVe>;
    string selectedChuyenXeId = ViewBag.SelectedChuyenXeId as string ?? "";
    string selectedNgayDi = ViewBag.SelectedNgayDi as string ?? "";
    string selectedTrangThaiVe = ViewBag.SelectedTrangThaiVe as string ?? "";
    string selectedTenKhach = ViewBag.SelectedTenKhach as string ?? "";
    
    var totalVe = Model != null ? Model.Count() : 0;
    var daThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 2) : 0;
    var chuaThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 1) : 0;
    var daHuyCount = Model != null ? Model.Count(v => (int)v.TrangThai == 4) : 0;
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-ticket-detailed me-2"></i>Quản lý đơn đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item active">Quản lý đặt vé</li>
            </ol>
        </div>
        <div class="d-flex gap-2">
            <a href="@Url.Action("QRScanner", "Booking")" class="btn btn-warning">
                <i class="bi bi-qr-code-scan me-1"></i>Quét QR
            </a>
            <a href="@Url.Action("BookingStatistics", "Booking")" class="btn btn-info">
                <i class="bi bi-graph-up me-1"></i>Thống kê
            </a>
            @if (!string.IsNullOrEmpty(selectedChuyenXeId))
            {
                <a href="@Url.Action("ExportPassengerList", "Booking", new { chuyenXeId = selectedChuyenXeId })" class="btn btn-success">
                    <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel danh sách hành khách
                </a>
            }
        </div>
    </div>
    
    <!-- Dashboard Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Tổng đơn đặt vé</h6>
                            <h3 class="my-2">@totalVe</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="bi bi-arrow-up me-1"></i>@(totalVe > 0 ? "100%" : "0%")</span>
                                <span class="text-nowrap">Trạng thái hiện tại</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-primary d-flex align-items-center justify-content-center">
                            <i class="bi bi-ticket-detailed text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Đã thanh toán</h6>
                            <h3 class="my-2">@daThanhToanCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="bi bi-arrow-up me-1"></i>@(totalVe > 0 ? $"{daThanhToanCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Tỷ lệ thanh toán</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-success d-flex align-items-center justify-content-center">
                            <i class="bi bi-cash-coin text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Chưa thanh toán</h6>
                            <h3 class="my-2">@chuaThanhToanCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-warning me-2"><i class="bi bi-exclamation-triangle me-1"></i>@(totalVe > 0 ? $"{chuaThanhToanCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Cần xử lý</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-warning d-flex align-items-center justify-content-center">
                            <i class="bi bi-hourglass-split text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Đã hủy</h6>
                            <h3 class="my-2">@daHuyCount</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-danger me-2"><i class="bi bi-x-circle me-1"></i>@(totalVe > 0 ? $"{daHuyCount * 100 / totalVe}%" : "0%")</span>
                                <span class="text-nowrap">Tỷ lệ hủy vé</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-danger d-flex align-items-center justify-content-center">
                            <i class="bi bi-x-octagon text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-funnel me-1"></i> Bộ lọc tìm kiếm
            </div>
            <button type="button" class="btn btn-sm btn-light" onclick="toggleAdvancedFilter()">
                <i class="bi bi-gear me-1"></i>Bộ lọc nâng cao
            </button>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end" id="filterForm">
                <!-- Basic filters -->
                <div class="col-md-3 col-sm-6">
                    <label class="form-label">Ngày đi</label>
                    <input type="date" name="ngayDi" class="form-control" value="@selectedNgayDi" />
                </div>
                <div class="col-md-3 col-sm-6">
                    <label class="form-label">Chuyến xe</label>
                    <select name="chuyenXeId" class="form-select">
                        <option value="">-- Chọn chuyến xe --</option>
                        @if (chuyenXeList != null)
                        {
                            foreach (var cx in chuyenXeList)
                            {
                                var tenTuyen = cx.TuyenDuong != null ? cx.TuyenDuong.TenTuyen : ($"{cx.DiemDi} - {cx.DiemDen}");
                                if (selectedChuyenXeId == cx.ChuyenXeId.ToString())
                                {
                                    <option value="@cx.ChuyenXeId" selected>@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                }
                                else
                                {
                                    <option value="@cx.ChuyenXeId">@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                }
                            }
                        }
                    </select>
                </div>
                <div class="col-md-2 col-sm-6">
                    <label class="form-label">Trạng thái vé</label>
                    <select name="trangThaiVe" class="form-select">
                        <option value="">-- Trạng thái vé --</option>
                        @if (trangThaiVeList != null)
                        {
                            foreach (var t in trangThaiVeList)
                            {
                                if (selectedTrangThaiVe == ((int)t).ToString())
                                {
                                    <option value="@((int)t)" selected>@t</option>
                                }
                                else
                                {
                                    <option value="@((int)t)">@t</option>
                                }
                            }
                        }
                    </select>
                </div>
                <div class="col-md-2 col-sm-6">
                    <label class="form-label">Tên khách</label>
                    <input type="text" name="tenKhach" class="form-control" value="@selectedTenKhach" placeholder="Tên khách" />
                </div>
                <div class="col-md-2 col-sm-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i>Tìm kiếm
                    </button>
                </div>

                <!-- Advanced filters (initially hidden) -->
                <div id="advancedFilter" style="display: none;" class="col-12">
                    <hr class="my-3">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Từ ngày đặt</label>
                            <input type="date" name="tuNgay" class="form-control" value="@ViewBag.TuNgayFilter" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Đến ngày đặt</label>
                            <input type="date" name="denNgay" class="form-control" value="@ViewBag.DenNgayFilter" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Số điện thoại</label>
                            <input type="text" name="soDienThoai" class="form-control" value="@ViewBag.SoDienThoaiFilter" placeholder="Nhập số điện thoại" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Email</label>
                            <input type="email" name="email" class="form-control" value="@ViewBag.EmailFilter" placeholder="Nhập email" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Mã vé</label>
                            <input type="text" name="maVe" class="form-control" value="@ViewBag.MaVeFilter" placeholder="Nhập mã vé" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Giá vé từ (VNĐ)</label>
                            <input type="number" name="giaVeMin" class="form-control" value="@ViewBag.GiaVeMinFilter" placeholder="0" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Giá vé đến (VNĐ)</label>
                            <input type="number" name="giaVeMax" class="form-control" value="@ViewBag.GiaVeMaxFilter" placeholder="1000000" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Trạng thái thanh toán</label>
                            <select name="trangThaiThanhToan" class="form-select">
                                <option value="">-- Tất cả --</option>
                                @{
                                    var trangThaiThanhToanFilter = ViewBag.TrangThaiThanhToanFilter as string;
                                }
                                <option value="true" selected="@(trangThaiThanhToanFilter == "true")">Đã thanh toán</option>
                                <option value="false" selected="@(trangThaiThanhToanFilter == "false")">Chưa thanh toán</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-success" onclick="exportWithFilters('csv')">
                                    <i class="bi bi-file-earmark-excel me-1"></i>Xuất CSV
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="exportWithFilters('excel')">
                                    <i class="bi bi-file-earmark-excel me-1"></i>Xuất Excel
                                </button>
                                <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Đặt lại tất cả
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-1"></i>Danh sách đơn đặt vé</h5>
            <div class="d-flex align-items-center gap-2">
                <button class="btn btn-sm btn-warning" id="bulkUpdateBtn" onclick="showBulkUpdateModal()" disabled>
                    <i class="bi bi-pencil-square me-1"></i>Cập nhật hàng loạt
                </button>
                <input type="text" id="searchTickets" class="form-control form-control-sm" placeholder="Tìm kiếm vé..." style="width: 200px;">
                <span class="badge bg-primary">@(Model != null ? Model.Count() : 0) đơn đặt vé</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="ticketsTable">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Mã vé</th>
                            <th>Tên khách</th>
                            <th>SĐT</th>
                            <th>Chuyến xe</th>
                            <th>Ghế</th>
                            <th>Trạng thái</th>
                            <th>Ngày đặt</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model != null && Model.Any())
                        {
                            foreach (var v in Model)
                            {
                                <tr data-search="@v.MaVe @v.TenKhach @v.SoDienThoai @v.ChuyenXe @v.Ghe">
                                    <td>
                                        <input type="checkbox" class="ticket-checkbox" value="@v.VeId" onchange="updateBulkActions()">
                                    </td>
                                    <td><span class="fw-medium">@v.MaVe</span></td>
                                    <td>@v.TenKhach</td>
                                    <td>@v.SoDienThoai</td>
                                    <td>@v.ChuyenXe</td>
                                    <td>@v.Ghe</td>
                                    <td>
                                        @{
                                            var ticketStatusClass = "";
                                            var ticketStatusText = "";
                                            var ticketStatusIcon = "";

                                            switch ((int)v.TrangThai)
                                            {
                                                case 1: // Đã đặt
                                                    ticketStatusClass = "bg-warning text-dark";
                                                    ticketStatusText = "Chưa thanh toán";
                                                    ticketStatusIcon = "bi-exclamation-circle";
                                                    break;
                                                case 2: // Đã thanh toán
                                                    ticketStatusClass = "bg-success";
                                                    ticketStatusText = "Đã thanh toán";
                                                    ticketStatusIcon = "bi-check-circle";
                                                    break;
                                                case 3: // Đã sử dụng
                                                    ticketStatusClass = "bg-info";
                                                    ticketStatusText = "Đã sử dụng";
                                                    ticketStatusIcon = "bi-person-check";
                                                    break;
                                                case 4: // Đã hủy
                                                    ticketStatusClass = "bg-danger";
                                                    ticketStatusText = "Đã hủy";
                                                    ticketStatusIcon = "bi-x-circle";
                                                    break;
                                                case 5: // Đã hoàn tiền
                                                    ticketStatusClass = "bg-secondary";
                                                    ticketStatusText = "Đã hoàn tiền";
                                                    ticketStatusIcon = "bi-arrow-return-left";
                                                    break;
                                                case 6: // Đã hoàn thành
                                                    ticketStatusClass = "bg-primary";
                                                    ticketStatusText = "Đã hoàn thành";
                                                    ticketStatusIcon = "bi-check-all";
                                                    break;
                                                default:
                                                    ticketStatusClass = "bg-warning text-dark";
                                                    ticketStatusText = "Chưa thanh toán";
                                                    ticketStatusIcon = "bi-exclamation-circle";
                                                    break;
                                            }
                                        }
                                        <span class="badge @ticketStatusClass"><i class="bi @ticketStatusIcon me-1"></i>@ticketStatusText</span>
                                    </td>
                                    <td>@(v.NgayDat.ToString("dd/MM/yyyy HH:mm"))</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item" href="@Url.Action("AdminBookingDetail", "Booking", new { id = v.VeId })"><i class="bi bi-eye me-2"></i>Xem chi tiết</a></li>
                                                <li><a class="dropdown-item" href="@Url.Action("PrintTicket", "Booking", new { id = v.VeId })" target="_blank"><i class="bi bi-printer me-2"></i>In vé</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@v.VeId, 'DaDon'); return false;"><i class="bi bi-check2-square me-2"></i>Đã đón</a></li>
                                                <li><a class="dropdown-item text-warning" href="#" onclick="updateTripStatus(@v.VeId, 'KhongCoMat'); return false;"><i class="bi bi-x-square me-2"></i>Không có mặt</a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="updateTripStatus(@v.VeId, 'HuyChuyen'); return false;"><i class="bi bi-arrow-repeat me-2"></i>Huỷ chuyến</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item add-note" href="#" data-id="@v.VeId"><i class="bi bi-pencil me-2"></i>Thêm ghi chú</a></li>
                                                <li><a class="dropdown-item send-notification" href="#" data-id="@v.VeId" data-name="@v.TenKhach"><i class="bi bi-envelope me-2"></i>Gửi thông báo</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                                        <p class="mt-3 mb-0">Không có dữ liệu</p>
                                        <p class="text-muted small">Hãy thử thay đổi bộ lọc tìm kiếm</p>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal thêm ghi chú -->
<div class="modal fade" id="noteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm ghi chú</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="noteVeId" value="">
                <div class="mb-3">
                    <label for="noteContent" class="form-label">Ghi chú</label>
                    <textarea class="form-control" id="noteContent" rows="4" placeholder="Nhập ghi chú..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="saveNote">Lưu ghi chú</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gửi thông báo khách hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">

                <!-- Customer Info -->
                <div class="alert alert-info">
                    <strong>Thông tin khách hàng:</strong><br>
                    <span id="customerInfo">Đang tải...</span>
                </div>

                <!-- Template Selection -->
                <div class="mb-3">
                    <label for="templateSelect" class="form-label">Chọn mẫu thông báo</label>
                    <select class="form-select" id="templateSelect" onchange="loadTemplate()">
                        <option value="">-- Chọn mẫu thông báo --</option>
                        <option value="confirm">Xác nhận đặt vé</option>
                        <option value="payment_success">Thanh toán thành công</option>
                        <option value="departure_reminder">Nhắc nhở khởi hành</option>
                        <option value="schedule_change">Thay đổi lịch trình</option>
                        <option value="cancellation">Hủy vé</option>
                        <option value="custom">Tùy chỉnh</option>
                    </select>
                </div>

                <!-- Notification Type -->
                <div class="mb-3">
                    <label class="form-label">Phương thức gửi</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sendEmail" checked>
                        <label class="form-check-label" for="sendEmail">
                            <i class="bi bi-envelope me-1"></i>Gửi Email
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sendSMS">
                        <label class="form-check-label" for="sendSMS">
                            <i class="bi bi-phone me-1"></i>Gửi SMS
                        </label>
                    </div>
                </div>

                <!-- Subject -->
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Tiêu đề thông báo">
                </div>

                <!-- Message -->
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="6" placeholder="Nội dung thông báo..."></textarea>
                    <div class="form-text">
                        Có thể sử dụng các biến: {TenKhach}, {MaVe}, {SoDienThoai}, {NgayKhoiHanh}, {TuyenDuong}, {SoGhe}
                    </div>
                </div>

                <!-- Preview -->
                <div class="mb-3">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="previewMessage()">
                        <i class="bi bi-eye me-1"></i>Xem trước
                    </button>
                </div>
                <div id="messagePreview" class="alert alert-light" style="display: none;">
                    <strong>Xem trước:</strong>
                    <div id="previewContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="sendNotification">
                    <i class="bi bi-send me-1"></i>Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal cập nhật hàng loạt -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cập nhật trạng thái hàng loạt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bạn đang cập nhật trạng thái cho <strong id="selectedCount">0</strong> vé.</p>
                <div class="mb-3">
                    <label class="form-label">Chọn trạng thái mới:</label>
                    <select class="form-select" id="bulkStatus">
                        <option value="DaThanhToan">Đã thanh toán</option>
                        <option value="DaSuDung">Đã sử dụng</option>
                        <option value="DaHoanThanh">Đã hoàn thành</option>
                        <option value="DaHuy">Đã hủy</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Ghi chú (tùy chọn):</label>
                    <textarea class="form-control" id="bulkNote" rows="3" placeholder="Nhập ghi chú..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-warning" id="executeBulkUpdate">Cập nhật</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');
            
            // Xử lý tìm kiếm
            $('#searchTickets').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $("#ticketsTable tbody tr").filter(function() {
                    var searchData = $(this).data('search').toLowerCase();
                    $(this).toggle(searchData.indexOf(value) > -1);
                });
            });
            
            // Xử lý modal thêm ghi chú
            $('.add-note').click(function(e) {
                e.preventDefault();
                $('#noteVeId').val($(this).data('id'));
                $('#noteContent').val('');
                $('#noteModal').modal('show');
            });
            
            // Xử lý lưu ghi chú
            $('#saveNote').click(function() {
                var veId = $('#noteVeId').val();
                var note = $('#noteContent').val();
                
                $.ajax({
                    url: '@Url.Action("AddNoteToTicket", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        ghiChu: note
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã cập nhật ghi chú', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật ghi chú', 'danger');
                    }
                });
                
                $('#noteModal').modal('hide');
            });
            
            // Xử lý modal gửi thông báo
            $('.send-notification').click(function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');
                
                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để...\n\nTrân trọng,\nNhà xe');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });
            
            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();
                var sendEmail = $('#sendEmail').is(':checked');
                var sendSMS = $('#sendSMS').is(':checked');

                if (!sendEmail && !sendSMS) {
                    showToast('Vui lòng chọn ít nhất một phương thức gửi', 'warning');
                    return;
                }

                if (!subject.trim() || !message.trim()) {
                    showToast('Vui lòng nhập đầy đủ tiêu đề và nội dung', 'warning');
                    return;
                }

                // Replace placeholders with actual data
                if (window.currentCustomer) {
                    const replacements = {
                        '{TenKhach}': window.currentCustomer.tenKhach || '',
                        '{MaVe}': window.currentCustomer.maVe || '',
                        '{SoDienThoai}': window.currentCustomer.soDienThoai || '',
                        '{NgayKhoiHanh}': '[Ngày khởi hành]',
                        '{TuyenDuong}': '[Tuyến đường]',
                        '{SoGhe}': '[Số ghế]'
                    };

                    Object.keys(replacements).forEach(key => {
                        subject = subject.replace(new RegExp(key, 'g'), replacements[key]);
                        message = message.replace(new RegExp(key, 'g'), replacements[key]);
                    });
                }

                $.ajax({
                    url: '@Url.Action("SendAdvancedNotification", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        veId: veId,
                        subject: subject,
                        message: message,
                        sendEmail: sendEmail,
                        sendSMS: sendSMS
                    }),
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            $('#notificationModal').modal('hide');

                            // Reset form
                            $('#templateSelect').val('');
                            $('#notificationSubject').val('');
                            $('#notificationMessage').val('');
                            $('#messagePreview').hide();
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    }
                });
            });

            // Xử lý bulk update
            $('#executeBulkUpdate').click(function() {
                var checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
                var veIds = Array.from(checkedBoxes).map(cb => cb.value);
                var status = $('#bulkStatus').val();
                var note = $('#bulkNote').val();

                if (veIds.length === 0) {
                    showToast('Vui lòng chọn ít nhất một vé', 'warning');
                    return;
                }

                $.ajax({
                    url: '@Url.Action("BulkUpdateStatus", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        VeIds: veIds,
                        TrangThai: status,
                        GhiChu: note
                    }),
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi cập nhật hàng loạt', 'danger');
                    }
                });

                $('#bulkUpdateModal').modal('hide');
            });
        });

        function updateTripStatus(veId, status) {
            let statusText = '';
            switch(status) {
                case 'DaDon': statusText = 'đã đón'; break;
                case 'KhongCoMat': statusText = 'không có mặt'; break;
                case 'HuyChuyen': statusText = 'huỷ chuyến'; break;
            }
            
            if (!confirm(`Xác nhận cập nhật trạng thái ${statusText} cho vé này?`)) return;
            
            fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `veId=${veId}&trangThai=${status}`
            })
            .then(r => r.json())
            .then(res => {
                if (res.success) {
                    showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(res.message || 'Có lỗi xảy ra', 'danger');
                }
            })
            .catch(err => {
                showToast('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
                console.error(err);
            });
        }

        // Bulk update functions
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const ticketCheckboxes = document.querySelectorAll('.ticket-checkbox');

            ticketCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const bulkBtn = document.getElementById('bulkUpdateBtn');

            if (checkedBoxes.length > 0) {
                bulkBtn.disabled = false;
                bulkBtn.innerHTML = `<i class="bi bi-pencil-square me-1"></i>Cập nhật ${checkedBoxes.length} vé`;
            } else {
                bulkBtn.disabled = true;
                bulkBtn.innerHTML = '<i class="bi bi-pencil-square me-1"></i>Cập nhật hàng loạt';
            }
        }

        function showBulkUpdateModal() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const count = checkedBoxes.length;

            if (count === 0) {
                showToast('Vui lòng chọn ít nhất một vé', 'warning');
                return;
            }

            document.getElementById('selectedCount').textContent = count;
            $('#bulkUpdateModal').modal('show');
        }

        // Advanced filter functions
        function toggleAdvancedFilter() {
            const advancedFilter = document.getElementById('advancedFilter');
            const isVisible = advancedFilter.style.display !== 'none';

            if (isVisible) {
                advancedFilter.style.display = 'none';
            } else {
                advancedFilter.style.display = 'block';
            }
        }

        function exportWithFilters(format = 'csv') {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            formData.append('format', format);
            const params = new URLSearchParams(formData);

            // Show loading
            showToast('Đang chuẩn bị file xuất...', 'info');

            // Create a temporary link to download
            const url = '@Url.Action("Export", "Booking")?' + params.toString();
            window.open(url, '_blank');
        }

        // Hiển thị modal gửi thông báo
        function showNotificationModal(veId) {
            $('#notificationVeId').val(veId);

            // Load customer info
            $.get('@Url.Action("GetTicketInfo", "Booking")', { veId: veId })
                .done(function(response) {
                    if (response.success) {
                        $('#customerInfo').html(`
                            <strong>${response.tenKhach}</strong><br>
                            Email: ${response.email}<br>
                            SĐT: ${response.soDienThoai}<br>
                            Mã vé: ${response.maVe}
                        `);

                        // Store customer data for template replacement
                        window.currentCustomer = response;
                    } else {
                        $('#customerInfo').text('Không thể tải thông tin khách hàng');
                    }
                })
                .fail(function() {
                    $('#customerInfo').text('Lỗi khi tải thông tin khách hàng');
                });

            $('#notificationModal').modal('show');
        }

        // Load notification templates
        function loadTemplate() {
            const template = $('#templateSelect').val();
            const templates = {
                confirm: {
                    subject: 'Xác nhận đặt vé thành công - {MaVe}',
                    message: `Kính chào {TenKhach},

Cảm ơn quý khách đã đặt vé tại hệ thống của chúng tôi.

Thông tin vé:
- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                payment_success: {
                    subject: 'Thanh toán thành công - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xác nhận đã nhận được thanh toán cho vé {MaVe}.

Thông tin chuyến đi:
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vé của quý khách đã được kích hoạt. Vui lòng mang theo giấy tờ tùy thân khi đi xe.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                departure_reminder: {
                    subject: 'Nhắc nhở: Chuyến xe của quý khách sắp khởi hành',
                    message: `Kính chào {TenKhach},

Đây là thông báo nhắc nhở về chuyến xe của quý khách:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Chúc quý khách có chuyến đi an toàn!

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                schedule_change: {
                    subject: 'Thông báo thay đổi lịch trình - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo có sự thay đổi về lịch trình chuyến xe:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Thời gian mới: [Vui lòng cập nhật thời gian mới]

Chúng tôi xin lỗi vì sự bất tiện này. Vui lòng liên hệ hotline để được hỗ trợ thêm.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                cancellation: {
                    subject: 'Thông báo hủy vé - {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo vé {MaVe} đã được hủy theo yêu cầu.

Thông tin vé đã hủy:
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ hotline để được hỗ trợ.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                }
            };

            if (templates[template]) {
                $('#notificationSubject').val(templates[template].subject);
                $('#notificationMessage').val(templates[template].message);
            } else if (template === 'custom') {
                $('#notificationSubject').val('');
                $('#notificationMessage').val('');
            }
        }

        // Preview message with customer data
        function previewMessage() {
            if (!window.currentCustomer) {
                showToast('Chưa có thông tin khách hàng để xem trước', 'warning');
                return;
            }

            let subject = $('#notificationSubject').val();
            let message = $('#notificationMessage').val();

            // Replace placeholders with actual data
            const replacements = {
                '{TenKhach}': window.currentCustomer.tenKhach || '',
                '{MaVe}': window.currentCustomer.maVe || '',
                '{SoDienThoai}': window.currentCustomer.soDienThoai || '',
                '{NgayKhoiHanh}': '[Ngày khởi hành]',
                '{TuyenDuong}': '[Tuyến đường]',
                '{SoGhe}': '[Số ghế]'
            };

            Object.keys(replacements).forEach(key => {
                subject = subject.replace(new RegExp(key, 'g'), replacements[key]);
                message = message.replace(new RegExp(key, 'g'), replacements[key]);
            });

            $('#previewContent').html(`
                <strong>Tiêu đề:</strong> ${subject}<br><br>
                <strong>Nội dung:</strong><br>
                <div style="white-space: pre-line; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;">
                    ${message}
                </div>
            `);
            $('#messagePreview').show();
        }

        // Hiển thị thông báo toast
        function showToast(message, type) {
            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">' + message + '</div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );
            
            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <style>
        .avatar-sm {
            height: 3rem;
            width: 3rem;
            font-size: 1.25rem;
        }
        
        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.75em;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
        }
        
        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            border: none;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
        }
        
        .table th, .table td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .table th {
            font-weight: 600;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,.02);
        }
        
        .dropdown-item {
            padding: 0.5rem 1rem;
        }
        
        .dropdown-item i {
            width: 1rem;
            text-align: center;
        }
        
        #searchTickets {
            border-radius: 20px;
            padding-left: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236c757d' class='bi bi-search' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 16px;
        }
    </style>
}
