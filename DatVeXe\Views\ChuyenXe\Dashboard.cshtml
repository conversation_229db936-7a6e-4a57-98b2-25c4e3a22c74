@{
    ViewData["Title"] = "Thống kê chuyến xe";
}

<div class="container py-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
        <h2 class="fw-bold text-primary mb-0 text-uppercase">Thống kê chuyến xe</h2>
        <div class="d-flex gap-2">
            <a asp-action="Index" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> Danh sách chuyến xe
            </a>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm chuyến xe
            </a>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-bus fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.TongChuyenXe</h3>
                    <p class="card-text">Tổng chuyến xe</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.ChuyenXeHomNay</h3>
                    <p class="card-text">Chuyến xe hôm nay</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.ChuyenXeThangNay</h3>
                    <p class="card-text">Chuyến xe tháng này</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.TongVeDaBan</h3>
                    <p class="card-text">Tổng vé đã bán</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê trạng thái -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Thống kê theo trạng thái
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">@ViewBag.ChuaKhoiHanh</h4>
                                <p class="text-muted">Chưa khởi hành</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-secondary">@ViewBag.DaKhoiHanh</h4>
                            <p class="text-muted">Đã khởi hành</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-route"></i> Top 5 tuyến đường phổ biến
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.TopTuyen != null && ((IEnumerable<dynamic>)ViewBag.TopTuyen).Any())
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var tuyen in (IEnumerable<dynamic>)ViewBag.TopTuyen)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <strong>@tuyen.TuyenDuong</strong>
                                        <br>
                                        <small class="text-muted">@tuyen.SoVeDaBan vé đã bán</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">@tuyen.SoChuyenXe</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted text-center">Chưa có dữ liệu</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê theo xe -->
    <div class="card">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">
                <i class="fas fa-truck"></i> Thống kê theo xe
            </h6>
        </div>
        <div class="card-body">
            @if (ViewBag.ThongKeXe != null && ((IEnumerable<dynamic>)ViewBag.ThongKeXe).Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="bg-light">
                            <tr>
                                <th>Biển số</th>
                                <th>Loại xe</th>
                                <th>Số ghế</th>
                                <th>Số chuyến xe</th>
                                <th>Tỷ lệ lấp đầy</th>
                                <th>Hiệu suất</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var xe in (IEnumerable<dynamic>)ViewBag.ThongKeXe)
                            {
                                <tr>
                                    <td><strong>@xe.BienSo</strong></td>
                                    <td>@xe.LoaiXe</td>
                                    <td>@xe.SoGhe chỗ</td>
                                    <td>@xe.SoChuyenXe chuyến</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar @(xe.TyLeLapDay >= 80 ? "bg-success" : xe.TyLeLapDay >= 60 ? "bg-warning" : "bg-danger")" 
                                                 role="progressbar" 
                                                 style="width: @(xe.TyLeLapDay)%">
                                                @(xe.TyLeLapDay.ToString("F1"))%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if (xe.TyLeLapDay >= 80)
                                        {
                                            <span class="badge bg-success">Tốt</span>
                                        }
                                        else if (xe.TyLeLapDay >= 60)
                                        {
                                            <span class="badge bg-warning">Trung bình</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Cần cải thiện</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <p class="text-muted text-center">Chưa có dữ liệu xe</p>
            }
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .progress {
            background-color: #e9ecef;
        }
        .list-group-item {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }
        .list-group-item:last-child {
            border-bottom: none;
        }
    </style>
}
