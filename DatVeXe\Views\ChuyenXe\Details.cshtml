@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Chi tiết chuyến xe";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> @ViewData["Title"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3">Thông tin chuyến xe</h6>
                            <dl class="row">
                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.DiemDi)</dt>
                                <dd class="col-sm-8">@Model.DiemDiDisplay</dd>

                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.DiemDen)</dt>
                                <dd class="col-sm-8">@Model.DiemDenDisplay</dd>

                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.NgayKhoiHanh)</dt>
                                <dd class="col-sm-8">@Model.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3">Thông tin xe</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Biển số xe</dt>
                                <dd class="col-sm-8">@Model.Xe.BienSo</dd>

                                <dt class="col-sm-4">Loại xe</dt>
                                <dd class="col-sm-8">@Model.Xe.LoaiXe</dd>

                                <dt class="col-sm-4">Số ghế</dt>
                                <dd class="col-sm-8">@Model.Xe.SoGhe chỗ</dd>

                                <dt class="col-sm-4">Số ghế trống</dt>
                                <dd class="col-sm-8">
                                    @{
                                        var soGheTrong = Model.Xe.SoGhe - (Model.Ves != null ? Model.Ves.Count : 0);
                                        if (soGheTrong > 0)
                                        {
                                            <span class="badge bg-success">@soGheTrong chỗ trống</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Hết chỗ</span>
                                        }
                                    }
                                </dd>

                                <dt class="col-sm-4">Tài xế</dt>
                                <dd class="col-sm-8">
                                    @if (Model.TaiXe != null)
                                    {
                                        <div>
                                            <strong>@Model.TaiXe.HoTen</strong>
                                            <br><small class="text-muted">@Model.TaiXe.SoDienThoai</small>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa gán tài xế</span>
                                    }
                                </dd>

                                <dt class="col-sm-4">Trạng thái</dt>
                                <dd class="col-sm-8">
                                    @if (Model.NgayKhoiHanh <= DateTime.Now)
                                    {
                                        <span class="badge bg-secondary">Đã khởi hành</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-primary">Chưa khởi hành</span>
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>

                    @if (Model.Ves.Any())
                    {
                        <div class="mt-4">
                            <h6 class="card-subtitle mb-3">Danh sách vé đã đặt (@Model.Ves.Count vé)</h6>
                            <div class="table-responsive">
                                <table class="table table-hover table-striped mb-0">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>Tên khách</th>
                                            <th>Số điện thoại</th>
                                            <th>Ngày đặt</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var ve in Model.Ves.OrderByDescending(v => v.NgayDat))
                                        {
                                            <tr>
                                                <td>@ve.TenKhach</td>
                                                <td>@ve.SoDienThoai</td>
                                                <td>@ve.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }

                    <div class="mt-4">
                        <div class="btn-group">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                            @if (Model.NgayKhoiHanh > DateTime.Now)
                            {
                                <a asp-action="Edit" asp-route-id="@Model.ChuyenXeId" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                @if (!Model.Ves.Any())
                                {
                                    <a asp-action="Delete" asp-route-id="@Model.ChuyenXeId" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> Xóa
                                    </a>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        dt {
            font-weight: 600;
        }
        .badge {
            font-weight: 500;
        }
    </style>
}