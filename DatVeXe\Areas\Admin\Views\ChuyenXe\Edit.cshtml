@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Sửa chuyến xe";
}
<div class="container mt-4">
    <h3><PERSON><PERSON><PERSON> chuyến xe</h3>
    <form asp-action="Edit" method="post">
        <input type="hidden" asp-for="ChuyenXeId" />
        <div class="form-group">
            <label>Xe</label>
            <select asp-for="XeId" class="form-control" asp-items="@(new SelectList(ViewBag.Xes, "XeId", "BienSo"))"></select>
        </div>
        <div class="form-group">
            <label><PERSON><PERSON>ến đường</label>
            <select asp-for="TuyenDuongId" class="form-control" asp-items="@(new SelectList(ViewBag.TuyenDuongs, "TuyenDuongId", "TenTuyen"))"></select>
        </div>
        <div class="form-group">
            <label>Ngày khởi hành</label>
            <input asp-for="NgayKhoiHanh" class="form-control" type="datetime-local" />
        </div>
        <div class="form-group">
            <label>Thời gian đi</label>
            <input asp-for="ThoiGianDi" class="form-control" type="time" />
        </div>
        <div class="form-group">
            <label>Giá vé</label>
            <input asp-for="Gia" class="form-control" />
        </div>
        <button type="submit" class="btn btn-primary">Lưu</button>
        <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Index" class="btn btn-secondary">Hủy</a>
    </form>
</div>
