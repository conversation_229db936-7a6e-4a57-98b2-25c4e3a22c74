namespace DatVeXe.Services
{
    public interface IEmailService
    {
        Task<bool> SendEmailAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null);
        Task<bool> SendTicketConfirmationAsync(string toEmail, string customerName, string ticketCode, 
            string tripInfo, string seatInfo, decimal price, DateTime departureTime);
        Task<bool> SendTicketCancellationAsync(string toEmail, string customerName, string ticketCode, 
            string tripInfo, string reason);
    }
}
