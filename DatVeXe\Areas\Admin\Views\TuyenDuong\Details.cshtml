@model DatVeXe.Models.TuyenDuong
@{
    ViewData["Title"] = "Chi tiết tuyến đường";
}
<h2>Chi tiết tuyến đường</h2>
<div>
    <dl class="row">
        <dt class="col-sm-2">Tê<PERSON> tuyến</dt>
        <dd class="col-sm-10">@Model.TenTuyen</dd>
        <dt class="col-sm-2">Điể<PERSON> đi</dt>
        <dd class="col-sm-10">@Model.DiemDi</dd>
        <dt class="col-sm-2"><PERSON><PERSON><PERSON><PERSON> đến</dt>
        <dd class="col-sm-10">@Model.DiemDen</dd>
        <dt class="col-sm-2">Khoảng cách</dt>
        <dd class="col-sm-10">@Model.KhoangCach km</dd>
        <dt class="col-sm-2">Thời gian dự kiến</dt>
        <dd class="col-sm-10">@Model.ThoiGianDuKien</dd>
        <dt class="col-sm-2">Gi<PERSON> vé</dt>
        <dd class="col-sm-10">@Model.GiaVe.ToString("N0")</dd>
        <dt class="col-sm-2">Mô tả</dt>
        <dd class="col-sm-10">@Model.MoTa</dd>
        <dt class="col-sm-2">Trạng thái</dt>
        <dd class="col-sm-10">@(Model.TrangThaiHoatDong ? "Hoạt động" : "Ngừng hoạt động")</dd>
    </dl>
    <a asp-action="Edit" asp-route-id="@Model.TuyenDuongId" class="btn btn-warning">Sửa</a>
    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
</div>
