@model dynamic
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Chi tiết đặt vé";
    var chuyenXe = Model.ChuyenXe;
    var choNgoi = Model.ChoNgoi;
    var thanhToans = Model.ThanhToans as IEnumerable<DatVeXe.Models.ThanhToan>;

    var statusClass = "";
    var statusText = "";
    var statusIcon = "";

    switch ((int)Model.VeTrangThai)
    {
        case 1: // Đã đặt
            statusClass = "bg-warning text-dark";
            statusText = "Chưa thanh toán";
            statusIcon = "bi-exclamation-circle";
            break;
        case 2: // Đã thanh toán
            statusClass = "bg-success";
            statusText = "Đã thanh toán";
            statusIcon = "bi-check-circle";
            break;
        case 3: // Đã sử dụng
            statusClass = "bg-info";
            statusText = "Đã sử dụng";
            statusIcon = "bi-person-check";
            break;
        case 4: // Đã hủy
            statusClass = "bg-danger";
            statusText = "Đã hủy";
            statusIcon = "bi-x-circle";
            break;
        case 5: // Đã hoàn tiền
            statusClass = "bg-secondary";
            statusText = "Đã hoàn tiền";
            statusIcon = "bi-arrow-return-left";
            break;
        case 6: // Đã hoàn thành
            statusClass = "bg-primary";
            statusText = "Đã hoàn thành";
            statusIcon = "bi-check-all";
            break;
        default:
            statusClass = "bg-warning text-dark";
            statusText = "Chưa thanh toán";
            statusIcon = "bi-exclamation-circle";
            break;
    }

    // Kiểm tra trạng thái thanh toán
    var daThanhToan = thanhToans != null && thanhToans.Any(t => t.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.ThanhCong);
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-ticket-detailed me-2"></i>Chi tiết đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Chi tiết đặt vé</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("PrintTicket", "Booking", new { id = Model.VeId })" class="btn btn-success" target="_blank">
                <i class="bi bi-printer me-1"></i>In vé
            </a>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Thông tin tổng quan -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge @statusClass fs-6 px-3 py-2">
                                        <i class="bi @statusIcon me-2"></i>@statusText
                                    </span>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-bold">@Model.TenKhach</h4>
                                    <p class="mb-0 text-muted">
                                        <i class="bi bi-ticket-detailed me-1"></i>Mã vé: <strong>@Model.MaVe</strong>
                                        <span class="mx-2">•</span>
                                        <i class="bi bi-calendar3 me-1"></i>@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <h3 class="mb-0 text-success fw-bold">@Model.TongTien.ToString("#,##0") VNĐ</h3>
                            <small class="text-muted">Tổng giá trị vé</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Thông tin chi tiết vé -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-person-circle me-2"></i>Thông tin khách hàng</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label"><i class="bi bi-person me-2"></i>Họ tên</label>
                                <div class="info-value">@Model.TenKhach</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label"><i class="bi bi-telephone me-2"></i>Số điện thoại</label>
                                <div class="info-value">@Model.SoDienThoai</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label"><i class="bi bi-envelope me-2"></i>Email</label>
                                <div class="info-value">@(string.IsNullOrEmpty(Model.Email) ? "Không có" : Model.Email)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label"><i class="bi bi-geo-alt me-2"></i>Số ghế</label>
                                <div class="info-value">
                                    <span class="badge bg-primary fs-6">@(string.IsNullOrEmpty(Model.DanhSachGhe) ? "Chưa chọn" : Model.DanhSachGhe)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thông tin chuyến xe -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-info text-white">
                    <h5 class="mb-0"><i class="bi bi-bus-front me-2"></i>Thông tin chuyến xe</h5>
                </div>
                <div class="card-body">
                    @if (chuyenXe != null)
                    {
                        <div class="row g-4">
                            <div class="col-md-12">
                                <div class="info-item">
                                    <label class="info-label"><i class="bi bi-signpost-2 me-2"></i>Tuyến đường</label>
                                    <div class="info-value">
                                        @(chuyenXe.TuyenDuong != null ? chuyenXe.TuyenDuong.TenTuyen : $"{chuyenXe.DiemDi} - {chuyenXe.DiemDen}")
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label"><i class="bi bi-calendar-event me-2"></i>Ngày khởi hành</label>
                                    <div class="info-value">@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label"><i class="bi bi-clock me-2"></i>Giờ khởi hành</label>
                                    <div class="info-value">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-3">
                            <i class="bi bi-exclamation-triangle fs-1"></i>
                            <p class="mt-2">Không có thông tin chuyến xe</p>
                        </div>
                    }
                </div>
            </div>

            @if (!string.IsNullOrEmpty(Model.GhiChu) || (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && !string.IsNullOrEmpty(Model.LyDoHuy)))
            {
                <!-- Ghi chú và lý do hủy -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-gradient-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-chat-left-text me-2"></i>Ghi chú</h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.GhiChu))
                        {
                            <div class="mb-3">
                                <label class="info-label"><i class="bi bi-sticky me-2"></i>Ghi chú</label>
                                <div class="info-value">@Model.GhiChu</div>
                            </div>
                        }
                        @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && !string.IsNullOrEmpty(Model.LyDoHuy))
                        {
                            <div class="alert alert-danger mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Lý do hủy:</strong> @Model.LyDoHuy
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Các hành động -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Hành động</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dropdown d-grid">
                                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-arrow-repeat me-2"></i>Cập nhật trạng thái
                                </button>
                                <ul class="dropdown-menu w-100">
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'DaDon'); return false;">
                                        <i class="bi bi-check2-square me-2 text-success"></i>Đã đón khách
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'KhongCoMat'); return false;">
                                        <i class="bi bi-x-square me-2 text-warning"></i>Không có mặt
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'HuyChuyen'); return false;">
                                        <i class="bi bi-arrow-repeat me-2 text-danger"></i>Hủy chuyến
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-primary w-100" id="btnSendNotification" data-id="@Model.VeId" data-name="@Model.TenKhach">
                                <i class="bi bi-envelope me-2"></i>Gửi thông báo
                            </button>
                        </div>
                        @if (!daThanhToan && Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy)
                        {
                            <div class="col-md-6">
                                <button class="btn btn-success w-100" onclick="confirmPayment(@Model.VeId)">
                                    <i class="bi bi-check-circle me-2"></i>Xác nhận thanh toán
                                </button>
                            </div>
                        }
                        @if (Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy && (chuyenXe == null || chuyenXe.NgayKhoiHanh > DateTime.Now))
                        {
                            <div class="col-md-6">
                                <button class="btn btn-danger w-100" onclick="showCancelModal()">
                                    <i class="bi bi-x-circle me-2"></i>Hủy vé
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar bên phải -->
        <div class="col-lg-4">
            <!-- Mã QR -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="mb-0"><i class="bi bi-qr-code me-2"></i>Mã QR vé</h5>
                </div>
                <div class="card-body text-center">
                    <div id="qrcode" class="mb-3"></div>
                    <p class="text-muted small mb-0">
                        <i class="bi bi-info-circle me-1"></i>
                        Quét mã QR để kiểm tra vé
                    </p>
                </div>
            </div>

            <!-- Lịch sử thanh toán -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-credit-card me-2"></i>Lịch sử thanh toán</h5>
                </div>
                <div class="card-body p-0">
                    @if (thanhToans != null && thanhToans.Any())
                    {
                        <div class="payment-history">
                            @foreach (var tt in thanhToans)
                            {
                                <div class="payment-item">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                @switch (tt.PhuongThucThanhToan)
                                                {
                                                    case DatVeXe.Models.PhuongThucThanhToan.TaiQuay:
                                                        <i class="bi bi-shop me-1"></i><span>Tại quầy</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.VNPay:
                                                        <i class="bi bi-credit-card me-1"></i><span>VNPay</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.MoMo:
                                                        <i class="bi bi-phone me-1"></i><span>MoMo</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ZaloPay:
                                                        <i class="bi bi-phone me-1"></i><span>ZaloPay</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ChuyenKhoan:
                                                        <i class="bi bi-bank me-1"></i><span>Chuyển khoản</span>
                                                        break;
                                                    default:
                                                        <i class="bi bi-question-circle me-1"></i><span>Khác</span>
                                                        break;
                                                }
                                            </h6>
                                            <small class="text-muted">@tt.MaGiaoDich</small>
                                        </div>
                                        <div class="text-end">
                                            @if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.ThanhCong)
                                            {
                                                <span class="badge bg-success mb-1">Thành công</span>
                                            }
                                            else if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.DangXuLy)
                                            {
                                                <span class="badge bg-warning text-dark mb-1">Đang xử lý</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger mb-1">Thất bại</span>
                                            }
                                            <div class="fw-bold">@tt.SoTien.ToString("#,##0") VNĐ</div>
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar3 me-1"></i>@tt.NgayThanhToan.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-credit-card text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3 mb-0 text-muted">Chưa có lịch sử thanh toán</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-envelope me-2"></i>Gửi thông báo</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label fw-bold">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Nhập tiêu đề email...">
                </div>
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label fw-bold">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="6" placeholder="Nhập nội dung thông báo..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Hủy
                </button>
                <button type="button" class="btn btn-primary" id="sendNotification">
                    <i class="bi bi-send me-1"></i>Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal hủy vé -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i>Hủy vé</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>
                    Bạn có chắc chắn muốn hủy vé này không? Hành động này không thể hoàn tác.
                </div>
                <div class="mb-3">
                    <label for="cancelReason" class="form-label fw-bold">Lý do hủy (tùy chọn)</label>
                    <textarea class="form-control" id="cancelReason" rows="3" placeholder="Nhập lý do hủy vé..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Không hủy
                </button>
                <button type="button" class="btn btn-danger" onclick="cancelBooking()">
                    <i class="bi bi-trash me-1"></i>Xác nhận hủy
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');

            // Tạo QR code
            var qr = new QRious({
                element: document.getElementById('qrcode'),
                value: '@Model.QRCode',
                size: 200,
                backgroundAlpha: 0,
                foreground: '#2c3e50',
                level: 'M'
            });

            // Xử lý modal gửi thông báo
            $('#btnSendNotification').click(function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');

                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để cập nhật thông tin về vé xe của bạn.\n\nMã vé: @Model.MaVe\nTên khách hàng: @Model.TenKhach\nChuyến xe: ' + '@(chuyenXe != null ? (chuyenXe.TuyenDuong != null ? chuyenXe.TuyenDuong.TenTuyen : $"{chuyenXe.DiemDi} - {chuyenXe.DiemDen}") : "")' + '\n\nTrân trọng,\nNhà xe ABC');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });

            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();

                if (!subject.trim()) {
                    showToast('Vui lòng nhập tiêu đề', 'warning');
                    return;
                }

                if (!message.trim()) {
                    showToast('Vui lòng nhập nội dung thông báo', 'warning');
                    return;
                }

                $(this).prop('disabled', true).html('<i class="bi bi-hourglass-split me-1"></i>Đang gửi...');

                $.ajax({
                    url: '@Url.Action("SendTicketNotification", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        subject: subject,
                        message: message
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã gửi thông báo thành công', 'success');
                            $('#notificationModal').modal('hide');
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    },
                    complete: function() {
                        $('#sendNotification').prop('disabled', false).html('<i class="bi bi-send me-1"></i>Gửi thông báo');
                    }
                });
            });
        });

        function updateTripStatus(veId, status) {
            let statusText = '';
            let confirmText = '';
            switch(status) {
                case 'DaDon':
                    statusText = 'đã đón khách';
                    confirmText = 'Xác nhận khách hàng đã được đón?';
                    break;
                case 'KhongCoMat':
                    statusText = 'không có mặt';
                    confirmText = 'Xác nhận khách hàng không có mặt?';
                    break;
                case 'HuyChuyen':
                    statusText = 'hủy chuyến';
                    confirmText = 'Xác nhận hủy chuyến xe này?';
                    break;
            }

            Swal.fire({
                title: 'Xác nhận',
                text: confirmText,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `veId=${veId}&trangThai=${status}`
                    })
                    .then(r => r.json())
                    .then(res => {
                        if (res.success) {
                            showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                            setTimeout(() => window.location.reload(), 1500);
                        } else {
                            showToast(res.message || 'Có lỗi xảy ra', 'danger');
                        }
                    })
                    .catch(err => {
                        showToast('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
                        console.error(err);
                    });
                }
            });
        }

        function confirmPayment(veId) {
            Swal.fire({
                title: 'Xác nhận thanh toán',
                text: 'Bạn có chắc chắn muốn xác nhận thanh toán cho vé này?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('@Url.Action("AdminConfirmPayment", "Booking")', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `id=${veId}`
                    })
                    .then(r => r.json())
                    .then(res => {
                        if (res.success) {
                            showToast(res.message || 'Xác nhận thanh toán thành công', 'success');
                            setTimeout(() => window.location.reload(), 1500);
                        } else {
                            showToast(res.message || 'Có lỗi xảy ra', 'danger');
                        }
                    })
                    .catch(err => {
                        showToast('Có lỗi xảy ra khi xác nhận thanh toán', 'danger');
                        console.error(err);
                    });
                }
            });
        }

        function showCancelModal() {
            $('#cancelModal').modal('show');
        }

        function cancelBooking() {
            var reason = $('#cancelReason').val();

            fetch('@Url.Action("AdminCancelBooking", "Booking")', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `id=@Model.VeId&lyDo=${encodeURIComponent(reason)}`
            })
            .then(r => r.json())
            .then(res => {
                if (res.success) {
                    showToast(res.message || 'Hủy vé thành công', 'success');
                    $('#cancelModal').modal('hide');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(res.message || 'Có lỗi xảy ra', 'danger');
                }
            })
            .catch(err => {
                showToast('Có lỗi xảy ra khi hủy vé', 'danger');
                console.error(err);
            });
        }

        // Hiển thị thông báo toast
        function showToast(message, type) {
            var iconClass = '';
            switch(type) {
                case 'success': iconClass = 'bi-check-circle'; break;
                case 'danger': iconClass = 'bi-exclamation-triangle'; break;
                case 'warning': iconClass = 'bi-exclamation-circle'; break;
                case 'info': iconClass = 'bi-info-circle'; break;
                default: iconClass = 'bi-info-circle'; break;
            }

            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">'+
                '    <i class="bi ' + iconClass + ' me-2"></i>' + message +
                '  </div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );

            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 4000 });
            bsToast.show();

            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <style>
        /* Gradient backgrounds */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .bg-gradient-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Card styles */
        .card {
            border-radius: 15px;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .card-header {
            border-bottom: none;
            padding: 1.25rem 1.5rem;
            border-radius: 15px 15px 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Info items */
        .info-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: block;
        }

        .info-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Payment history */
        .payment-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .payment-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }

        .payment-item:hover {
            background-color: #f8f9fa;
        }

        .payment-item:last-child {
            border-bottom: none;
        }

        /* QR Code styling */
        #qrcode canvas {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Button improvements */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Badge improvements */
        .badge {
            font-size: 0.875rem;
            padding: 0.5em 1em;
            border-radius: 20px;
        }

        /* Modal improvements */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-bottom: none;
            border-radius: 15px 15px 0 0;
        }

        .modal-footer {
            border-top: none;
            border-radius: 0 0 15px 15px;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }

            .info-item {
                padding: 0.75rem;
            }

            .payment-item {
                padding: 0.75rem 1rem;
            }
        }
    </style>
}

