@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Quét mã QR vé";
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-qr-code-scan me-2"></i>Quét mã QR vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Quét QR</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- QR Scanner -->
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-camera me-1"></i>Quét mã QR</h5>
                </div>
                <div class="card-body">
                    <div id="qr-reader" style="width: 100%; min-height: 300px;"></div>
                    <div class="mt-3">
                        <button id="start-scan" class="btn btn-success me-2">
                            <i class="bi bi-play-fill me-1"></i>Bắt đầu quét
                        </button>
                        <button id="stop-scan" class="btn btn-danger me-2" disabled>
                            <i class="bi bi-stop-fill me-1"></i>Dừng quét
                        </button>
                        <button id="switch-camera" class="btn btn-info" disabled>
                            <i class="bi bi-camera-reels me-1"></i>Đổi camera
                        </button>
                    </div>
                    
                    <!-- Manual input -->
                    <hr class="my-4">
                    <h6>Hoặc nhập mã vé thủ công:</h6>
                    <div class="input-group">
                        <input type="text" id="manual-code" class="form-control" placeholder="Nhập mã vé...">
                        <button class="btn btn-outline-primary" onclick="checkManualCode()">
                            <i class="bi bi-search me-1"></i>Kiểm tra
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Info -->
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-ticket-detailed me-1"></i>Thông tin vé</h5>
                </div>
                <div class="card-body">
                    <div id="ticket-info" class="text-center text-muted">
                        <i class="bi bi-qr-code-scan" style="font-size: 4rem; opacity: 0.3;"></i>
                        <p class="mt-3">Quét mã QR để xem thông tin vé</p>
                    </div>
                    
                    <div id="ticket-details" style="display: none;">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="alert alert-info" id="ticket-status"></div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Mã vé:</label>
                                <p id="ticket-code" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Tên khách:</label>
                                <p id="customer-name" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Số điện thoại:</label>
                                <p id="customer-phone" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Số ghế:</label>
                                <p id="seat-number" class="mb-2"></p>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Tuyến đường:</label>
                                <p id="route-info" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Ngày khởi hành:</label>
                                <p id="departure-date" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Biển số xe:</label>
                                <p id="vehicle-number" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Giá vé:</label>
                                <p id="ticket-price" class="mb-2"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Trạng thái:</label>
                                <p id="ticket-state" class="mb-2"></p>
                            </div>
                        </div>
                        
                        <div class="mt-4" id="action-buttons" style="display: none;">
                            <div class="mb-3">
                                <label for="note-input" class="form-label">Ghi chú (tùy chọn):</label>
                                <textarea id="note-input" class="form-control" rows="2" placeholder="Nhập ghi chú..."></textarea>
                            </div>
                            <button class="btn btn-success w-100" onclick="markTicketUsed()">
                                <i class="bi bi-check-circle me-1"></i>Đánh dấu đã sử dụng
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Scans -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-1"></i>Lịch sử quét gần đây</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>Mã vé</th>
                                    <th>Tên khách</th>
                                    <th>Trạng thái</th>
                                    <th>Kết quả</th>
                                </tr>
                            </thead>
                            <tbody id="scan-history">
                                <tr>
                                    <td colspan="5" class="text-center text-muted">Chưa có lịch sử quét</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <script>
        let html5QrcodeScanner;
        let currentQrData = '';
        let scanHistory = [];

        // Initialize QR scanner
        function initQRScanner() {
            html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader",
                { 
                    fps: 10, 
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0
                },
                false
            );
        }

        // Start scanning
        function startScanning() {
            html5QrcodeScanner.render(onScanSuccess, onScanFailure);
            document.getElementById('start-scan').disabled = true;
            document.getElementById('stop-scan').disabled = false;
            document.getElementById('switch-camera').disabled = false;
        }

        // Stop scanning
        function stopScanning() {
            html5QrcodeScanner.clear();
            document.getElementById('start-scan').disabled = false;
            document.getElementById('stop-scan').disabled = true;
            document.getElementById('switch-camera').disabled = true;
        }

        // QR scan success callback
        function onScanSuccess(decodedText, decodedResult) {
            currentQrData = decodedText;
            verifyQRCode(decodedText);
            addToScanHistory(decodedText, 'Đang kiểm tra...');
        }

        // QR scan failure callback
        function onScanFailure(error) {
            // Handle scan failure, usually better to ignore
        }

        // Verify QR code
        function verifyQRCode(qrData) {
            fetch(`@Url.Action("VerifyQRCode", "Booking")?qrData=${encodeURIComponent(qrData)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTicketInfo(data.ticketInfo, true);
                        updateScanHistory(qrData, 'Thành công');
                    } else {
                        displayError(data.message);
                        updateScanHistory(qrData, 'Thất bại: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    displayError('Có lỗi xảy ra khi kiểm tra mã QR');
                    updateScanHistory(qrData, 'Lỗi hệ thống');
                });
        }

        // Display ticket information
        function displayTicketInfo(ticketInfo, canMarkUsed = false) {
            document.getElementById('ticket-info').style.display = 'none';
            document.getElementById('ticket-details').style.display = 'block';

            document.getElementById('ticket-code').textContent = ticketInfo.maVe;
            document.getElementById('customer-name').textContent = ticketInfo.tenKhach;
            document.getElementById('customer-phone').textContent = ticketInfo.soDienThoai;
            document.getElementById('seat-number').textContent = ticketInfo.soGhe || 'Chưa chọn';
            document.getElementById('route-info').textContent = ticketInfo.tuyenDuong;
            document.getElementById('departure-date').textContent = ticketInfo.ngayKhoiHanh;
            document.getElementById('vehicle-number').textContent = ticketInfo.bienSoXe || 'Chưa xác định';
            document.getElementById('ticket-price').textContent = ticketInfo.giaVe + ' VNĐ';
            document.getElementById('ticket-state').textContent = ticketInfo.trangThai;

            const statusElement = document.getElementById('ticket-status');
            statusElement.textContent = 'Vé hợp lệ - Có thể sử dụng';
            statusElement.className = 'alert alert-success';

            const actionButtons = document.getElementById('action-buttons');
            if (canMarkUsed && ticketInfo.trangThai === 'Đã thanh toán') {
                actionButtons.style.display = 'block';
            } else {
                actionButtons.style.display = 'none';
            }
        }

        // Display error
        function displayError(message) {
            document.getElementById('ticket-info').style.display = 'none';
            document.getElementById('ticket-details').style.display = 'block';
            document.getElementById('action-buttons').style.display = 'none';

            const statusElement = document.getElementById('ticket-status');
            statusElement.textContent = message;
            statusElement.className = 'alert alert-danger';

            // Clear other fields
            ['ticket-code', 'customer-name', 'customer-phone', 'seat-number', 
             'route-info', 'departure-date', 'vehicle-number', 'ticket-price', 'ticket-state'].forEach(id => {
                document.getElementById(id).textContent = '-';
            });
        }

        // Mark ticket as used
        function markTicketUsed() {
            const note = document.getElementById('note-input').value;
            
            fetch('@Url.Action("MarkTicketUsed", "Booking")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    QrData: currentQrData,
                    GhiChu: note
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    document.getElementById('action-buttons').style.display = 'none';
                    document.getElementById('ticket-state').textContent = 'Đã sử dụng';
                    updateScanHistory(currentQrData, 'Đã đánh dấu sử dụng');
                } else {
                    showToast(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Có lỗi xảy ra khi cập nhật trạng thái vé', 'danger');
            });
        }

        // Check manual code
        function checkManualCode() {
            const code = document.getElementById('manual-code').value.trim();
            if (!code) {
                showToast('Vui lòng nhập mã vé', 'warning');
                return;
            }

            // Create QR data format
            const qrData = `TICKET:${code}|CUSTOMER:|PHONE:|SEAT:|DATE:`;
            currentQrData = qrData;
            verifyQRCode(qrData);
            addToScanHistory(code, 'Đang kiểm tra...');
        }

        // Scan history management
        function addToScanHistory(code, status) {
            const now = new Date();
            scanHistory.unshift({
                time: now.toLocaleTimeString('vi-VN'),
                code: code.includes('TICKET:') ? code.split('|')[0].replace('TICKET:', '') : code,
                customer: '',
                status: status,
                result: status
            });
            updateScanHistoryTable();
        }

        function updateScanHistory(code, result) {
            if (scanHistory.length > 0) {
                scanHistory[0].result = result;
                updateScanHistoryTable();
            }
        }

        function updateScanHistoryTable() {
            const tbody = document.getElementById('scan-history');
            if (scanHistory.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Chưa có lịch sử quét</td></tr>';
                return;
            }

            tbody.innerHTML = scanHistory.slice(0, 10).map(item => `
                <tr>
                    <td>${item.time}</td>
                    <td>${item.code}</td>
                    <td>${item.customer || '-'}</td>
                    <td>${item.status}</td>
                    <td>
                        <span class="badge ${item.result.includes('Thành công') || item.result.includes('sử dụng') ? 'bg-success' : 
                                           item.result.includes('Thất bại') ? 'bg-danger' : 'bg-warning'}">
                            ${item.result}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        // Toast notification
        function showToast(message, type) {
            // Simple toast implementation
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initQRScanner();
            
            document.getElementById('start-scan').addEventListener('click', startScanning);
            document.getElementById('stop-scan').addEventListener('click', stopScanning);
            
            // Auto-start scanning
            setTimeout(startScanning, 1000);
        });
    </script>
}
