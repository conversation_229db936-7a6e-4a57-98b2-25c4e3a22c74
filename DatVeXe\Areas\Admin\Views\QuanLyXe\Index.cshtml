@model IEnumerable<DatVeXe.Models.Xe>
@{
    ViewData["Title"] = "Quản lý xe";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-bus" style="color: #e74c3c;"></i>
        Quản lý xe buýt
    </h3>
    <div class="d-flex gap-2">
        <a asp-action="ThongKe" class="btn btn-info">
            <i class="fas fa-chart-bar"></i>
            Thống kê
        </a>
        <a asp-action="Create" class="btn" style="background-color: #e74c3c; border-color: #e74c3c; color: black; font-weight: 500;">
            <i class="fas fa-plus"></i>
            Thêm xe mới
        </a>
    </div>
</div>

@if (TempData["ThongBao"] != null)
{
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        @TempData["ThongBao"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" asp-action="Index">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label" style="color: black;">Tìm kiếm</label>
                    <input type="text" name="searchString" value="@ViewBag.CurrentFilter"
                           class="form-control" placeholder="Biển số xe, loại xe..." />
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Loại xe</label>
                    <select name="loaiXe" class="form-select">
                        <option value="">Tất cả</option>
                        <option value="Giường nằm" selected="@(ViewBag.LoaiXeFilter == "Giường nằm")">Giường nằm</option>
                        <option value="Ghế ngồi" selected="@(ViewBag.LoaiXeFilter == "Ghế ngồi")">Ghế ngồi</option>
                        <option value="Limousine" selected="@(ViewBag.LoaiXeFilter == "Limousine")">Limousine</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Trạng thái</label>
                    <select name="trangThai" class="form-select">
                        <option value="">Tất cả</option>
                        <option value="true" selected="@(ViewBag.TrangThaiFilter == "true")">Hoạt động</option>
                        <option value="false" selected="@(ViewBag.TrangThaiFilter == "false")">Ngừng hoạt động</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Sắp xếp</label>
                    <select name="sortOrder" class="form-select">
                        <option value="" selected="@(ViewBag.CurrentSort == "")">Biển số A-Z</option>
                        <option value="bien_so_desc" selected="@(ViewBag.CurrentSort == "bien_so_desc")">Biển số Z-A</option>
                        <option value="so_ghe" selected="@(ViewBag.CurrentSort == "so_ghe")">Số ghế tăng dần</option>
                        <option value="so_ghe_desc" selected="@(ViewBag.CurrentSort == "so_ghe_desc")">Số ghế giảm dần</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Tìm
                    </button>
                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Vehicles Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Danh sách xe buýt (@ViewBag.TotalCount)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>Biển số xe</th>
                        <th>Loại xe</th>
                        <th>Số ghế</th>
                        <th>Năm sản xuất</th>
                        <th>Trạng thái</th>
                        <th>Chuyến xe</th>
                        <th>Tỷ lệ sử dụng</th>
                        <th width="200">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr id="<EMAIL>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-bus text-primary me-2 fs-4"></i>
                                    <div>
                                        <strong>@item.BienSoXe</strong>
                                        @if (!string.IsNullOrEmpty(item.MoTa))
                                        {
                                            <br><small class="text-muted">@item.MoTa</small>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-@(item.LoaiXe == "Limousine" ? "warning" :
                                                       item.LoaiXe == "Giường nằm" ? "info" : "secondary")">
                                    @item.LoaiXe
                                </span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="fs-5">@item.SoGhe</strong>
                                    <br><small class="text-muted">ghế</small>
                                </div>
                            </td>
                            <td>
                                @if (item.NamSanXuat.HasValue)
                                {
                                    <span class="badge bg-secondary">@item.NamSanXuat</span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa cập nhật</span>
                                }
                            </td>
                            <td>
                                <span class="badge bg-@(item.TrangThaiHoatDong ? "success" : "danger")"
                                      id="<EMAIL>">
                                    <i class="fas fa-@(item.TrangThaiHoatDong ? "check" : "times") me-1"></i>
                                    @(item.TrangThaiHoatDong ? "Hoạt động" : "Ngừng hoạt động")
                                </span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>@(item.ChuyenXes?.Count ?? 0)</strong>
                                    <br><small class="text-muted">chuyến</small>
                                </div>
                            </td>
                            <td>
                                @{
                                    var totalTrips = item.ChuyenXes?.Count ?? 0;
                                    var completedTrips = item.ChuyenXes?.Count(c => c.NgayKhoiHanh < DateTime.Now) ?? 0;
                                    var utilizationRate = totalTrips > 0 ? (completedTrips * 100 / totalTrips) : 0;
                                }
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-@(utilizationRate >= 80 ? "success" : utilizationRate >= 50 ? "warning" : "danger")"
                                         role="progressbar" style="width: @utilizationRate%">
                                        @utilizationRate%
                                    </div>
                                </div>
                                <small class="text-muted">@completedTrips/@totalTrips chuyến</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@item.XeId"
                                       class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.XeId"
                                       class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-@(item.TrangThaiHoatDong ? "danger" : "success")"
                                            onclick="toggleStatus(@item.XeId)"
                                            title="@(item.TrangThaiHoatDong ? "Vô hiệu hóa" : "Kích hoạt")">
                                        <i class="fas fa-@(item.TrangThaiHoatDong ? "pause" : "play")"></i>
                                    </button>
                                    @if ((item.ChuyenXes?.Count ?? 0) == 0)
                                    {
                                        <a asp-action="Delete" asp-route-id="@item.XeId"
                                           class="btn btn-sm btn-outline-danger" title="Xóa"
                                           onclick="return confirm('Bạn có chắc chắn muốn xóa xe này?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Phân trang xe" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage - 1, searchString = ViewBag.CurrentFilter, loaiXe = ViewBag.LoaiXeFilter, trangThai = ViewBag.TrangThaiFilter, sortOrder = ViewBag.CurrentSort })">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Index", new { page = i, searchString = ViewBag.CurrentFilter, loaiXe = ViewBag.LoaiXeFilter, trangThai = ViewBag.TrangThaiFilter, sortOrder = ViewBag.CurrentSort })">
                        @i
                    </a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage + 1, searchString = ViewBag.CurrentFilter, loaiXe = ViewBag.LoaiXeFilter, trangThai = ViewBag.TrangThaiFilter, sortOrder = ViewBag.CurrentSort })">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

@section Scripts {
    <script>
        function toggleStatus(xeId) {
            if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái xe này?')) {
                $.post('@Url.Action("ToggleStatus")', { xeId: xeId })
                    .done(function(data) {
                        if (data.success) {
                            // Update badge
                            const badge = $('#status-badge-' + xeId);
                            if (data.isActive) {
                                badge.removeClass('bg-danger').addClass('bg-success');
                                badge.html('<i class="fas fa-check me-1"></i>Hoạt động');
                            } else {
                                badge.removeClass('bg-success').addClass('bg-danger');
                                badge.html('<i class="fas fa-times me-1"></i>Ngừng hoạt động');
                            }

                            showToast(data.message, 'success');
                        } else {
                            showToast(data.message, 'error');
                        }
                    })
                    .fail(function() {
                        showToast('Có lỗi xảy ra khi thay đổi trạng thái', 'error');
                    });
            }
        }

        function showToast(message, type) {
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const toast = $(`
                <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }

            $('#toast-container').append(toast);
            toast.toast('show');

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}
