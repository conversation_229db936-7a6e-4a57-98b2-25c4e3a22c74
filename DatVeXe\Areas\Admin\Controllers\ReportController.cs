using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;
using DatVeXe.Attributes;
using System.Text;
using OfficeOpenXml;

namespace DatVeXe.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminAuthorization]
    public class ReportController : Controller
    {
        private readonly DatVeXeContext _context;

        public ReportController(DatVeXeContext context)
        {
            _context = context;
        }

        // GET: Admin/Report/Dashboard
        public async Task<IActionResult> Dashboard()
        {
            var now = DateTime.Now;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
            var startOfYear = new DateTime(now.Year, 1, 1);

            // Báo cáo tổng quan
            var tongDoanhThu = await _context.ThanhToans
                .Where(t => t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .SumAsync(t => t.SoTien);

            var doanhThuThangNay = await _context.ThanhToans
                .Where(t => t.NgayThanhToan >= startOfMonth && 
                           t.NgayThanhToan <= endOfMonth &&
                           t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .SumAsync(t => t.SoTien);

            var doanhThuNamNay = await _context.ThanhToans
                .Where(t => t.NgayThanhToan >= startOfYear &&
                           t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .SumAsync(t => t.SoTien);

            // Số lượng vé bán
            var tongVeBan = await _context.Ves.CountAsync();
            var veBanThangNay = await _context.Ves
                .Where(v => v.NgayDat >= startOfMonth && v.NgayDat <= endOfMonth)
                .CountAsync();

            // Tỷ lệ lấp đầy
            var tongGhe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .SumAsync(c => c.Xe.SoGhe);
            var tyLeLapDay = tongGhe > 0 ? (double)tongVeBan / tongGhe * 100 : 0;

            // Doanh thu theo tháng (12 tháng gần nhất)
            var doanhThuTheoThang = new List<object>();
            for (int i = 11; i >= 0; i--)
            {
                var thang = now.AddMonths(-i);
                var startMonth = new DateTime(thang.Year, thang.Month, 1);
                var endMonth = startMonth.AddMonths(1).AddDays(-1);
                
                var doanhThu = await _context.ThanhToans
                    .Where(t => t.NgayThanhToan >= startMonth && 
                               t.NgayThanhToan <= endMonth &&
                               t.TrangThai == TrangThaiThanhToan.ThanhCong)
                    .SumAsync(t => t.SoTien);

                doanhThuTheoThang.Add(new {
                    Thang = thang.ToString("MM/yyyy"),
                    DoanhThu = doanhThu
                });
            }

            // Top tuyến đường theo doanh thu
            var topTuyenDuong = await _context.ThanhToans
                .Include(t => t.Ve)
                    .ThenInclude(v => v.ChuyenXe)
                        .ThenInclude(c => c.TuyenDuong)
                .Where(t => t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .GroupBy(t => new { 
                    t.Ve.ChuyenXe.TuyenDuong.DiemDi,
                    t.Ve.ChuyenXe.TuyenDuong.DiemDen
                })
                .Select(g => new {
                    TuyenDuong = $"{g.Key.DiemDi} → {g.Key.DiemDen}",
                    DoanhThu = g.Sum(x => x.SoTien),
                    SoVe = g.Count()
                })
                .OrderByDescending(x => x.DoanhThu)
                .Take(10)
                .ToListAsync();

            ViewBag.TongDoanhThu = tongDoanhThu;
            ViewBag.DoanhThuThangNay = doanhThuThangNay;
            ViewBag.DoanhThuNamNay = doanhThuNamNay;
            ViewBag.TongVeBan = tongVeBan;
            ViewBag.VeBanThangNay = veBanThangNay;
            ViewBag.TyLeLapDay = tyLeLapDay;
            ViewBag.DoanhThuTheoThang = doanhThuTheoThang;
            ViewBag.TopTuyenDuong = topTuyenDuong;

            return View();
        }

        // GET: Admin/Report/Revenue
        public async Task<IActionResult> Revenue(DateTime? fromDate, DateTime? toDate, string groupBy = "day")
        {
            fromDate = fromDate ?? DateTime.Now.AddDays(-30);
            toDate = toDate ?? DateTime.Now;

            var thanhToans = await _context.ThanhToans
                .Include(t => t.Ve)
                    .ThenInclude(v => v.ChuyenXe)
                        .ThenInclude(c => c.TuyenDuong)
                .Where(t => t.NgayThanhToan.Date >= fromDate.Value.Date && 
                           t.NgayThanhToan.Date <= toDate.Value.Date &&
                           t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .ToListAsync();

            // Group data based on groupBy parameter
            var groupedData = groupBy switch
            {
                "month" => thanhToans.GroupBy(t => new { t.NgayThanhToan.Year, t.NgayThanhToan.Month })
                                   .Select(g => new {
                                       Period = $"{g.Key.Month:00}/{g.Key.Year}",
                                       DoanhThu = g.Sum(x => x.SoTien),
                                       SoGiaoDich = g.Count()
                                   }).ToList(),
                "week" => thanhToans.GroupBy(t => GetWeekOfYear(t.NgayThanhToan))
                                  .Select(g => new {
                                      Period = $"Tuần {g.Key}",
                                      DoanhThu = g.Sum(x => x.SoTien),
                                      SoGiaoDich = g.Count()
                                  }).ToList(),
                _ => thanhToans.GroupBy(t => t.NgayThanhToan.Date)
                             .Select(g => new {
                                 Period = g.Key.ToString("dd/MM/yyyy"),
                                 DoanhThu = g.Sum(x => x.SoTien),
                                 SoGiaoDich = g.Count()
                             }).ToList()
            };

            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;
            ViewBag.GroupBy = groupBy;
            ViewBag.TongDoanhThu = thanhToans.Sum(t => t.SoTien);
            ViewBag.TongGiaoDich = thanhToans.Count;
            ViewBag.GroupedData = groupedData;

            return View();
        }

        // GET: Admin/Report/Customer
        public async Task<IActionResult> Customer()
        {
            var now = DateTime.Now;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);

            // Thống kê khách hàng
            var tongKhachHang = await _context.NguoiDungs.CountAsync();
            var khachHangMoi = await _context.NguoiDungs
                .Where(u => u.NgayDangKy >= startOfMonth)
                .CountAsync();

            // Top khách hàng theo số vé đã mua
            var topKhachHang = await _context.Ves
                .Include(v => v.NguoiDung)
                .GroupBy(v => new { v.NguoiDungId, v.NguoiDung.HoTen, v.NguoiDung.Email })
                .Select(g => new {
                    KhachHang = g.Key.HoTen,
                    Email = g.Key.Email,
                    SoVe = g.Count(),
                    TongTien = g.Sum(x => x.GiaVe)
                })
                .OrderByDescending(x => x.SoVe)
                .Take(10)
                .ToListAsync();

            // Phân tích theo độ tuổi (nếu có thông tin ngày sinh)
            var phanTichDoTuoi = await _context.NguoiDungs
                .Where(u => u.NgaySinh.HasValue)
                .Select(u => new {
                    Tuoi = now.Year - u.NgaySinh.Value.Year
                })
                .GroupBy(x => x.Tuoi < 25 ? "Dưới 25" :
                             x.Tuoi < 35 ? "25-34" :
                             x.Tuoi < 45 ? "35-44" :
                             x.Tuoi < 55 ? "45-54" : "Trên 55")
                .Select(g => new {
                    NhomTuoi = g.Key,
                    SoLuong = g.Count()
                })
                .ToListAsync();

            ViewBag.TongKhachHang = tongKhachHang;
            ViewBag.KhachHangMoi = khachHangMoi;
            ViewBag.TopKhachHang = topKhachHang;
            ViewBag.PhanTichDoTuoi = phanTichDoTuoi;

            return View();
        }

        // GET: Admin/Report/Route
        public async Task<IActionResult> Route()
        {
            // Thống kê theo tuyến đường
            var thongKeTuyenDuong = await _context.TuyenDuongs
                .Include(t => t.ChuyenXes)
                    .ThenInclude(c => c.Ves)
                .Select(t => new {
                    TuyenDuong = $"{t.DiemDi} → {t.DiemDen}",
                    SoChuyenXe = t.ChuyenXes.Count,
                    SoVeDaBan = t.ChuyenXes.Sum(c => c.Ves != null ? c.Ves.Count : 0),
                    DoanhThu = t.ChuyenXes.Sum(c => c.Ves != null ? c.Ves.Sum(v => v.GiaVe) : 0),
                    TyLeLapDay = t.ChuyenXes.Any() ? 
                         (double)t.ChuyenXes.Sum(c => c.Ves != null ? c.Ves.Count : 0) / t.ChuyenXes.Sum(c => c.Xe != null ? c.Xe.SoGhe : 0) * 100 : 0
                })
                .OrderByDescending(x => x.DoanhThu)
                .ToListAsync();

            return View(thongKeTuyenDuong);
        }

        // Export CSV Report
        public async Task<IActionResult> ExportCsv(string reportType, DateTime? fromDate, DateTime? toDate)
        {
            var csv = new StringBuilder();

            switch (reportType)
            {
                case "revenue":
                    csv = await CreateRevenueCsvReport(fromDate, toDate);
                    break;
                case "customer":
                    csv = await CreateCustomerCsvReport();
                    break;
                case "route":
                    csv = await CreateRouteCsvReport();
                    break;
                default:
                    csv = await CreateOverviewCsvReport();
                    break;
            }

            var fileName = $"BaoCao_{reportType}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var bytes = Encoding.UTF8.GetBytes(csv.ToString());
            return File(bytes, "text/csv", fileName);
        }

        private async Task<StringBuilder> CreateRevenueCsvReport(DateTime? fromDate, DateTime? toDate)
        {
            fromDate = fromDate ?? DateTime.Now.AddDays(-30);
            toDate = toDate ?? DateTime.Now;

            var data = await _context.ThanhToans
                .Include(t => t.Ve)
                    .ThenInclude(v => v.ChuyenXe)
                        .ThenInclude(c => c.TuyenDuong)
                .Where(t => t.NgayThanhToan.Date >= fromDate.Value.Date &&
                           t.NgayThanhToan.Date <= toDate.Value.Date &&
                           t.TrangThai == TrangThaiThanhToan.ThanhCong)
                .Select(t => new {
                    t.NgayThanhToan,
                    t.SoTien,
                    TuyenDuong = $"{t.Ve.ChuyenXe.TuyenDuong.DiemDi} → {t.Ve.ChuyenXe.TuyenDuong.DiemDen}",
                    t.PhuongThucThanhToan
                })
                .ToListAsync();

            var csv = new StringBuilder();
            csv.AppendLine("Ngày,Tuyến đường,Số tiền,Phương thức");

            foreach (var item in data)
            {
                csv.AppendLine($"{item.NgayThanhToan:dd/MM/yyyy}," +
                              $"\"{item.TuyenDuong}\"," +
                              $"{item.SoTien}," +
                              $"\"{item.PhuongThucThanhToan}\"");
            }

            return csv;
        }

        private async Task CreateCustomerReport(ExcelPackage package)
        {
            var worksheet = package.Workbook.Worksheets.Add("Báo cáo khách hàng");
            
            var data = await _context.NguoiDungs
                .Include(u => u.Ves)
                .Select(u => new {
                    u.HoTen,
                    u.Email,
                    u.SoDienThoai,
                    u.NgayDangKy,
                    SoVeDaMua = u.Ves.Count,
                    TongTienDaChiTieu = u.Ves.Sum(v => v.GiaVe)
                })
                .OrderByDescending(x => x.SoVeDaMua)
                .ToListAsync();

            // Headers
            worksheet.Cells[1, 1].Value = "Họ tên";
            worksheet.Cells[1, 2].Value = "Email";
            worksheet.Cells[1, 3].Value = "Số điện thoại";
            worksheet.Cells[1, 4].Value = "Ngày đăng ký";
            worksheet.Cells[1, 5].Value = "Số vé đã mua";
            worksheet.Cells[1, 6].Value = "Tổng tiền đã chi tiêu";

            // Data
            for (int i = 0; i < data.Count; i++)
            {
                var row = i + 2;
                worksheet.Cells[row, 1].Value = data[i].HoTen;
                worksheet.Cells[row, 2].Value = data[i].Email;
                worksheet.Cells[row, 3].Value = data[i].SoDienThoai;
                worksheet.Cells[row, 4].Value = data[i].NgayDangKy.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 5].Value = data[i].SoVeDaMua;
                worksheet.Cells[row, 6].Value = data[i].TongTienDaChiTieu;
            }

            // Format
            worksheet.Cells[1, 1, 1, 6].Style.Font.Bold = true;
            worksheet.Cells.AutoFitColumns();
        }

        private async Task CreateRouteReport(ExcelPackage package)
        {
            var worksheet = package.Workbook.Worksheets.Add("Báo cáo tuyến đường");
            
            var data = await _context.TuyenDuongs
                .Include(t => t.ChuyenXes)
                    .ThenInclude(c => c.Ves)
                .Select(t => new {
                    TuyenDuong = $"{t.DiemDi} → {t.DiemDen}",
                    t.KhoangCach,
                    t.GiaVe,
                    SoChuyenXe = t.ChuyenXes.Count,
                    SoVeDaBan = t.ChuyenXes.Sum(c => c.Ves.Count),
                    DoanhThu = t.ChuyenXes.Sum(c => c.Ves.Sum(v => v.GiaVe))
                })
                .OrderByDescending(x => x.DoanhThu)
                .ToListAsync();

            // Headers and data similar to above...
            worksheet.Cells[1, 1].Value = "Tuyến đường";
            worksheet.Cells[1, 2].Value = "Khoảng cách (km)";
            worksheet.Cells[1, 3].Value = "Giá vé";
            worksheet.Cells[1, 4].Value = "Số chuyến xe";
            worksheet.Cells[1, 5].Value = "Số vé đã bán";
            worksheet.Cells[1, 6].Value = "Doanh thu";

            for (int i = 0; i < data.Count; i++)
            {
                var row = i + 2;
                worksheet.Cells[row, 1].Value = data[i].TuyenDuong;
                worksheet.Cells[row, 2].Value = data[i].KhoangCach;
                worksheet.Cells[row, 3].Value = data[i].GiaVe;
                worksheet.Cells[row, 4].Value = data[i].SoChuyenXe;
                worksheet.Cells[row, 5].Value = data[i].SoVeDaBan;
                worksheet.Cells[row, 6].Value = data[i].DoanhThu;
            }

            worksheet.Cells[1, 1, 1, 6].Style.Font.Bold = true;
            worksheet.Cells.AutoFitColumns();
        }

        private async Task CreateOverviewReport(ExcelPackage package)
        {
            // Create overview report with multiple sheets
            // Revenue report will be created separately
            await CreateCustomerReport(package);
            await CreateRouteReport(package);
        }

        private async Task<StringBuilder> CreateCustomerCsvReport()
        {
            var csv = new StringBuilder();
            csv.AppendLine("Họ tên,Email,Số điện thoại,Ngày đăng ký,Số vé đã mua,Tổng tiền đã chi tiêu");

            var data = await _context.NguoiDungs
                .Include(u => u.Ves)
                .Select(u => new {
                    u.HoTen,
                    u.Email,
                    u.SoDienThoai,
                    u.NgayDangKy,
                    SoVeDaMua = u.Ves.Count,
                    TongTienDaChiTieu = u.Ves.Sum(v => v.GiaVe)
                })
                .OrderByDescending(x => x.SoVeDaMua)
                .ToListAsync();

            foreach (var item in data)
            {
                csv.AppendLine($"\"{item.HoTen}\",\"{item.Email}\",\"{item.SoDienThoai}\",\"{item.NgayDangKy:dd/MM/yyyy}\",{item.SoVeDaMua},{item.TongTienDaChiTieu}");
            }

            return csv;
        }

        private async Task<StringBuilder> CreateRouteCsvReport()
        {
            var csv = new StringBuilder();
            csv.AppendLine("Tuyến đường,Khoảng cách (km),Giá vé,Số chuyến xe,Số vé đã bán,Doanh thu");

            var data = await _context.TuyenDuongs
                .Include(t => t.ChuyenXes)
                    .ThenInclude(c => c.Ves)
                .Select(t => new {
                    TuyenDuong = $"{t.DiemDi} → {t.DiemDen}",
                    t.KhoangCach,
                    t.GiaVe,
                    SoChuyenXe = t.ChuyenXes.Count,
                    SoVeDaBan = t.ChuyenXes.Sum(c => c.Ves.Count),
                    DoanhThu = t.ChuyenXes.Sum(c => c.Ves.Sum(v => v.GiaVe))
                })
                .OrderByDescending(x => x.DoanhThu)
                .ToListAsync();

            foreach (var item in data)
            {
                csv.AppendLine($"\"{item.TuyenDuong}\",{item.KhoangCach},{item.GiaVe},{item.SoChuyenXe},{item.SoVeDaBan},{item.DoanhThu}");
            }

            return csv;
        }

        private async Task<StringBuilder> CreateOverviewCsvReport()
        {
            var csv = new StringBuilder();
            csv.AppendLine("Báo cáo tổng quan hệ thống");
            csv.AppendLine("");

            // Thống kê cơ bản
            var tongNguoiDung = await _context.NguoiDungs.CountAsync();
            var tongVe = await _context.Ves.CountAsync();
            var tongTuyenDuong = await _context.TuyenDuongs.CountAsync();
            var tongXe = await _context.Xes.CountAsync();

            csv.AppendLine($"Tổng người dùng,{tongNguoiDung}");
            csv.AppendLine($"Tổng vé đã bán,{tongVe}");
            csv.AppendLine($"Tổng tuyến đường,{tongTuyenDuong}");
            csv.AppendLine($"Tổng xe buýt,{tongXe}");

            return csv;
        }

        private int GetWeekOfYear(DateTime date)
        {
            var culture = System.Globalization.CultureInfo.CurrentCulture;
            return culture.Calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        }
    }
}
