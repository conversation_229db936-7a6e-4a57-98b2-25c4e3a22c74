@model DatVeXe.Models.TuyenDuong
@{
    ViewData["Title"] = "Sửa tuyến đường";
}
<h2>Sửa tuyến đường</h2>
<form asp-action="Edit" method="post">
    <input type="hidden" asp-for="TuyenDuongId" />
    <div class="form-group">
        <label asp-for="TenTuyen"></label>
        <input asp-for="TenTuyen" class="form-control" />
        <span asp-validation-for="TenTuyen" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="DiemDi"></label>
        <input asp-for="DiemDi" class="form-control" />
        <span asp-validation-for="DiemDi" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="DiemDen"></label>
        <input asp-for="DiemDen" class="form-control" />
        <span asp-validation-for="DiemDen" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="KhoangCach"></label>
        <input asp-for="KhoangCach" class="form-control" />
        <span asp-validation-for="KhoangCach" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="ThoiGianDuKien"></label>
        <input asp-for="ThoiGianDuKien" class="form-control" type="time" />
        <span asp-validation-for="ThoiGianDuKien" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="MoTa"></label>
        <textarea asp-for="MoTa" class="form-control"></textarea>
        <span asp-validation-for="MoTa" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="GiaVe"></label>
        <input asp-for="GiaVe" class="form-control" />
        <span asp-validation-for="GiaVe" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="TrangThaiHoatDong"></label>
        <input asp-for="TrangThaiHoatDong" type="checkbox" />
    </div>
    <button type="submit" class="btn btn-success">Lưu</button>
    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
</form>
