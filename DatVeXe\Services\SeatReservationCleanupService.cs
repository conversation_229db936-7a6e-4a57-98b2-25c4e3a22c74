using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;

namespace DatVeXe.Services
{
    public class SeatReservationCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SeatReservationCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(1); // Chạy mỗi phút

        public SeatReservationCleanupService(
            IServiceProvider serviceProvider,
            ILogger<SeatReservationCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CleanupExpiredReservations();
                    await Task.Delay(_cleanupInterval, stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while cleaning up expired seat reservations");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Đợi 5 phút nếu có lỗi
                }
            }
        }

        private async Task CleanupExpiredReservations()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<DatVeXeContext>();

            var expiredReservations = await context.SeatReservations
                .Where(r => r.IsActive && r.ExpiresAt <= DateTime.Now)
                .ToListAsync();

            if (expiredReservations.Any())
            {
                foreach (var reservation in expiredReservations)
                {
                    reservation.IsActive = false;
                }

                await context.SaveChangesAsync();
                _logger.LogInformation($"Cleaned up {expiredReservations.Count} expired seat reservations");
            }
        }
    }
}
