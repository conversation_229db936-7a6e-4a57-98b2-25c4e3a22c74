@model DatVeXe.Models.Xe
@{
    ViewData["Title"] = "Thêm xe mới";
}
<h2>Thêm xe mới</h2>
@if (TempData["ThongBao"] != null)
{
    <div class="alert alert-info">@TempData["ThongBao"]</div>
}
<form asp-action="Create">
    <div class="form-group">
        <label asp-for="BienSoXe"></label>
        <input asp-for="BienSoXe" class="form-control" />
        <span asp-validation-for="BienSoXe" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="LoaiXe"></label>
        <input asp-for="LoaiXe" class="form-control" />
        <span asp-validation-for="LoaiXe" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="SoGhe"></label>
        <input asp-for="SoGhe" class="form-control" />
        <span asp-validation-for="SoGhe" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="MoTa"></label>
        <input asp-for="MoTa" class="form-control" />
        <span asp-validation-for="MoTa" class="text-danger"></span>
    </div>
    <button type="submit" class="btn btn-primary">Lưu</button>
    <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
</form>
