using DatVeXe.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace DatVeXe.Controllers
{
    public class BookingManagementController : Controller
    {
        private readonly DatVeXeContext _context;

        public BookingManagementController(DatVeXeContext context)
        {
            _context = context;
        }

        // Kiểm tra đăng nhập và quyền admin
        private bool IsLoggedIn()
        {
            return HttpContext.Session.GetInt32("UserId").HasValue;
        }

        private bool IsAdmin()
        {
            return HttpContext.Session.GetInt32("IsAdmin") == 1;
        }

        // GET: BookingManagement - Danh sách chuyến xe để quản lý
        public async Task<IActionResult> Index(DateTime? ngayKhoiHanh, string? trangThai)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return RedirectToAction("Auth", "TaiKhoan");
            }

            var query = _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.TaiXe)
                .Include(c => c.Ves)
                .AsQueryable();

            // Lọc theo ngày khởi hành
            if (ngayKhoiHanh.HasValue)
            {
                query = query.Where(c => c.NgayKhoiHanh.Date == ngayKhoiHanh.Value.Date);
            }
            else
            {
                // Mặc định hiển thị chuyến xe trong 7 ngày tới
                var fromDate = DateTime.Today;
                var toDate = DateTime.Today.AddDays(7);
                query = query.Where(c => c.NgayKhoiHanh.Date >= fromDate && c.NgayKhoiHanh.Date <= toDate);
            }

            // Lọc theo trạng thái
            if (!string.IsNullOrEmpty(trangThai))
            {
                switch (trangThai)
                {
                    case "chua_khoi_hanh":
                        query = query.Where(c => c.NgayKhoiHanh > DateTime.Now);
                        break;
                    case "da_khoi_hanh":
                        query = query.Where(c => c.NgayKhoiHanh <= DateTime.Now);
                        break;
                }
            }

            var chuyenXes = await query
                .OrderBy(c => c.NgayKhoiHanh)
                .ToListAsync();

            ViewBag.NgayKhoiHanh = ngayKhoiHanh;
            ViewBag.TrangThai = trangThai;

            return View(chuyenXes);
        }

        // GET: BookingManagement/Details/5 - Chi tiết booking của chuyến xe
        public async Task<IActionResult> Details(int id)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return RedirectToAction("Auth", "TaiKhoan");
            }

            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.TaiXe)
                .Include(c => c.Ves)
                    .ThenInclude(v => v.ChoNgoi)
                .Include(c => c.Ves)
                    .ThenInclude(v => v.ThanhToans)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            return View(chuyenXe);
        }

        // POST: BookingManagement/UpdateTicketStatus - Cập nhật trạng thái vé
        [HttpPost]
        public async Task<IActionResult> UpdateTicketStatus(int veId, TrangThaiVe trangThai, string? ghiChu)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return Json(new { success = false, message = "Không có quyền truy cập" });
            }

            try
            {
                var ve = await _context.Ves.FindAsync(veId);
                if (ve == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy vé" });
                }

                var oldStatus = ve.TrangThai;
                ve.TrangThai = trangThai;
                
                if (!string.IsNullOrEmpty(ghiChu))
                {
                    ve.GhiChu = ghiChu;
                }

                // Cập nhật thời gian tương ứng
                switch (trangThai)
                {
                    case TrangThaiVe.DaHuy:
                        ve.NgayHuy = DateTime.Now;
                        ve.LyDoHuy = ghiChu;
                        break;
                    case TrangThaiVe.DaSuDung:
                        // Đánh dấu đã sử dụng (đã đón khách)
                        break;
                    case TrangThaiVe.DaHoanThanh:
                        // Đánh dấu hoàn thành chuyến đi
                        break;
                }

                await _context.SaveChangesAsync();

                return Json(new { 
                    success = true, 
                    message = $"Đã cập nhật trạng thái vé từ '{GetStatusText(oldStatus)}' thành '{GetStatusText(trangThai)}'" 
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        // POST: BookingManagement/UpdateMultipleTickets - Cập nhật nhiều vé cùng lúc
        [HttpPost]
        public async Task<IActionResult> UpdateMultipleTickets(List<int> veIds, TrangThaiVe trangThai, string? ghiChu)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return Json(new { success = false, message = "Không có quyền truy cập" });
            }

            try
            {
                var ves = await _context.Ves.Where(v => veIds.Contains(v.VeId)).ToListAsync();
                
                if (!ves.Any())
                {
                    return Json(new { success = false, message = "Không tìm thấy vé nào" });
                }

                foreach (var ve in ves)
                {
                    ve.TrangThai = trangThai;
                    
                    if (!string.IsNullOrEmpty(ghiChu))
                    {
                        ve.GhiChu = ghiChu;
                    }

                    // Cập nhật thời gian tương ứng
                    switch (trangThai)
                    {                    case TrangThaiVe.DaHuy:
                        ve.NgayHuy = DateTime.Now;
                        ve.LyDoHuy = ghiChu;
                        break;
                    case TrangThaiVe.DaSuDung:
                        // Đánh dấu đã sử dụng (đã đón khách)
                        break;
                    case TrangThaiVe.DaHoanThanh:
                        // Đánh dấu hoàn thành chuyến đi
                        break;
                    }
                }

                await _context.SaveChangesAsync();

                return Json(new { 
                    success = true, 
                    message = $"Đã cập nhật {ves.Count} vé thành trạng thái '{GetStatusText(trangThai)}'" 
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }
        
        // POST: BookingManagement/SendNotification - Gửi thông báo cho hành khách
        [HttpPost]
        public async Task<IActionResult> SendNotification(List<int> veIds, string subject, string message)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return Json(new { success = false, message = "Không có quyền truy cập" });
            }
            
            try
            {
                if (string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(message))
                {
                    return Json(new { success = false, message = "Vui lòng nhập tiêu đề và nội dung thông báo" });
                }
                
                var ves = await _context.Ves
                    .Where(v => veIds.Contains(v.VeId))
                    .ToListAsync();
                
                if (!ves.Any())
                {
                    return Json(new { success = false, message = "Không tìm thấy vé nào" });
                }
                
                // Đây là nơi thực hiện gửi email (giả lập thành công cho mục đích demo)
                
                return Json(new { 
                    success = true, 
                    message = $"Đã gửi thông báo đến {ves.Count} hành khách thành công" 
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        // GET: BookingManagement/ExportPassengerList/5 - Xuất danh sách hành khách
        public async Task<IActionResult> ExportPassengerList(int id)
        {
            if (!IsLoggedIn() || !IsAdmin())
            {
                return RedirectToAction("Auth", "TaiKhoan");
            }

            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.TaiXe)
                .Include(c => c.Ves)
                    .ThenInclude(v => v.ChoNgoi)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction(nameof(Index));
            }

            // Tạo CSV content
            var csv = new StringBuilder();
            csv.AppendLine("STT,Tên khách hàng,Số điện thoại,Email,Số ghế,Trạng thái,Ngày đặt,Ghi chú");

            var stt = 1;
            foreach (var ve in chuyenXe.Ves.OrderBy(v => v.ChoNgoi?.SoGhe))
            {
                csv.AppendLine($"{stt},{ve.TenKhach},{ve.SoDienThoai},{ve.Email},{ve.ChoNgoi?.SoGhe},{GetStatusText(ve.TrangThai)},{ve.NgayDat:dd/MM/yyyy HH:mm},{ve.GhiChu}");
                stt++;
            }

            var fileName = $"DanhSachHanhKhach_{chuyenXe.DiemDiDisplay}_{chuyenXe.DiemDenDisplay}_{chuyenXe.NgayKhoiHanh:yyyyMMdd_HHmm}.csv";
            var bytes = Encoding.UTF8.GetBytes(csv.ToString());

            return File(bytes, "text/csv", fileName);
        }

        private string GetStatusText(TrangThaiVe trangThai)
        {
            return trangThai switch
            {
                TrangThaiVe.DaDat => "Đã đặt",
                TrangThaiVe.DaThanhToan => "Đã thanh toán",
                TrangThaiVe.DaSuDung => "Đã sử dụng",
                TrangThaiVe.DaHoanThanh => "Đã hoàn thành",
                TrangThaiVe.DaHuy => "Đã hủy",
                TrangThaiVe.DaHoanTien => "Đã hoàn tiền",
                _ => "Không xác định"
            };
        }
    }
}
