using DatVeXe.Models;
using DatVeXe.Services;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Đăng ký DbContext sử dụng SQL Server LocalDB
builder.Services.AddDbContext<DatVeXeContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Cấu hình Email Settings
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));

// Đăng ký Email Service
builder.Services.AddScoped<IEmailService, EmailService>();

// Đăng ký SMS Service
builder.Services.AddScoped<ISMSService, SMSService>();

// Đăng ký Payment Service
builder.Services.AddScoped<IPaymentService, PaymentService>();

// Đăng ký Trip Search Service
builder.Services.AddScoped<ITripSearchService, TripSearchService>();

// Đăng ký Background Service để dọn dẹp reservation hết hạn
builder.Services.AddHostedService<SeatReservationCleanupService>();

// Thêm hỗ trợ Session
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30); // Thời gian timeout của session
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Sử dụng Session
app.UseSession();

// Cấu hình routing cho Admin Area
app.MapControllerRoute(
    name: "admin",
    pattern: "{area:exists}/{controller=Admin}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
