@model IEnumerable<DatVeXe.Models.DanhGiaChuyenDi>
@{
    ViewData["Title"] = "Quản lý đánh giá";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-star" style="color: #f39c12;"></i>
        Quản lý đánh giá
    </h3>
    <div class="d-flex gap-2">
        <a asp-action="Statistics" class="btn btn-info">
            <i class="fas fa-chart-bar"></i>
            Thống kê
        </a>
        <a asp-action="Export" class="btn btn-success" 
           onclick="exportWithFilters()">
            <i class="fas fa-file-excel"></i>
            Xuất Excel
        </a>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" asp-action="Index" id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Tìm kiếm</label>
                    <input type="text" name="searchString" value="@ViewBag.CurrentFilter" 
                           class="form-control" placeholder="Tên khách hàng, nội dung, tuyến đường..." />
                </div>
                <div class="col-md-2">
                    <label class="form-label">Điểm đánh giá</label>
                    <select name="diemDanhGia" class="form-select">
                        <option value="">Tất cả</option>
                        @for (int i = 1; i <= 5; i++)
                        {
                            <option value="@i" selected="@(ViewBag.DiemDanhGiaFilter == i)">@i sao</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Trạng thái</label>
                    <select name="trangThai" class="form-select">
                        <option value="">Tất cả</option>
                        <option value="true" selected="@(ViewBag.TrangThaiFilter == true)">Hiển thị</option>
                        <option value="false" selected="@(ViewBag.TrangThaiFilter == false)">Ẩn</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Từ ngày</label>
                    <input type="date" name="tuNgay" value="@ViewBag.TuNgayFilter" class="form-control" />
                </div>
                <div class="col-md-2">
                    <label class="form-label">Đến ngày</label>
                    <input type="date" name="denNgay" value="@ViewBag.DenNgayFilter" class="form-control" />
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-1">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Reviews Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Danh sách đánh giá (@ViewBag.TotalCount)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>Khách hàng</th>
                        <th>Tuyến đường</th>
                        <th>Điểm đánh giá</th>
                        <th>Nội dung</th>
                        <th>Trạng thái</th>
                        <th>Thời gian</th>
                        <th width="150">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var danhGia in Model)
                    {
                        <tr id="<EMAIL>">
                            <td>
                                <div>
                                    <strong>@danhGia.NguoiDung?.HoTen</strong>
                                    <br><small class="text-muted">@danhGia.NguoiDung?.Email</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>@danhGia.Ve?.ChuyenXe?.TuyenDuong?.DiemDi → @danhGia.Ve?.ChuyenXe?.TuyenDuong?.DiemDen</strong>
                                    <br><small class="text-muted">@danhGia.Ve?.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy")</small>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="text-warning mb-1">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= danhGia.DiemDanhGia ? "" : "text-muted")"></i>
                                        }
                                    </div>
                                    <span class="badge bg-@(GetRatingBadgeClass(danhGia.DiemDanhGia))">
                                        @danhGia.DiemDanhGia/5
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div style="max-width: 300px;">
                                    @if (!string.IsNullOrEmpty(danhGia.NoiDung))
                                    {
                                        <p class="mb-0">
                                            @(danhGia.NoiDung.Length > 100 ? danhGia.NoiDung.Substring(0, 100) + "..." : danhGia.NoiDung)
                                        </p>
                                        @if (danhGia.NoiDung.Length > 100)
                                        {
                                            <small>
                                                <a href="#" onclick="showFullContent(@danhGia.DanhGiaId)" class="text-primary">
                                                    Xem thêm
                                                </a>
                                            </small>
                                        }
                                    }
                                    else
                                    {
                                        <em class="text-muted">Không có nội dung</em>
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-@(!danhGia.BiAn ? "success" : "secondary")"
                                      id="<EMAIL>">
                                    <i class="fas fa-@(!danhGia.BiAn ? "eye" : "eye-slash") me-1"></i>
                                    @(!danhGia.BiAn ? "Hiển thị" : "Ẩn")
                                </span>
                            </td>
                            <td>
                                <div>
                                    <strong>@danhGia.ThoiGianDanhGia.ToString("dd/MM/yyyy")</strong>
                                    <br><small class="text-muted">@danhGia.ThoiGianDanhGia.ToString("HH:mm")</small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@danhGia.DanhGiaId" 
                                       class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-@(!danhGia.BiAn ? "warning" : "success")"
                                            onclick="toggleStatus(@danhGia.DanhGiaId)"
                                            title="@(!danhGia.BiAn ? "Ẩn" : "Hiển thị")">
                                        <i class="fas fa-@(!danhGia.BiAn ? "eye-slash" : "eye")"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteReview(@danhGia.DanhGiaId)" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Phân trang đánh giá" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@GetPageUrl(ViewBag.CurrentPage - 1)">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@GetPageUrl(i)">@i</a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@GetPageUrl(ViewBag.CurrentPage + 1)">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Full Content Modal -->
<div class="modal fade" id="fullContentModal" tabindex="-1" aria-labelledby="fullContentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullContentModalLabel">Nội dung đánh giá đầy đủ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="fullContentBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetRatingBadgeClass(int rating)
    {
        return rating switch
        {
            5 => "success",
            4 => "primary",
            3 => "warning",
            2 => "danger",
            1 => "dark",
            _ => "secondary"
        };
    }

    private string GetPageUrl(int page)
    {
        return Url.Action("Index", new { 
            page = page, 
            searchString = ViewBag.CurrentFilter, 
            diemDanhGia = ViewBag.DiemDanhGiaFilter,
            trangThai = ViewBag.TrangThaiFilter,
            tuNgay = ViewBag.TuNgayFilter,
            denNgay = ViewBag.DenNgayFilter
        }) ?? string.Empty;
    }
}

@section Scripts {
    <script>
        function toggleStatus(danhGiaId) {
            if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái hiển thị của đánh giá này?')) {
                $.post('@Url.Action("ToggleStatus")', { danhGiaId: danhGiaId })
                    .done(function(data) {
                        if (data.success) {
                            // Update badge
                            const badge = $('#status-badge-' + danhGiaId);
                            if (data.isVisible) {
                                badge.removeClass('bg-secondary').addClass('bg-success');
                                badge.html('<i class="fas fa-eye me-1"></i>Hiển thị');
                            } else {
                                badge.removeClass('bg-success').addClass('bg-secondary');
                                badge.html('<i class="fas fa-eye-slash me-1"></i>Ẩn');
                            }

                            showToast(data.message, 'success');
                        } else {
                            showToast(data.message, 'error');
                        }
                    })
                    .fail(function() {
                        showToast('Có lỗi xảy ra khi thay đổi trạng thái', 'error');
                    });
            }
        }

        function deleteReview(danhGiaId) {
            if (confirm('Bạn có chắc chắn muốn xóa đánh giá này? Hành động này không thể hoàn tác.')) {
                $.post('@Url.Action("Delete")', { danhGiaId: danhGiaId })
                    .done(function(data) {
                        if (data.success) {
                            // Remove row
                            $('#review-row-' + danhGiaId).fadeOut(500, function() {
                                $(this).remove();
                            });

                            showToast(data.message, 'success');
                        } else {
                            showToast(data.message, 'error');
                        }
                    })
                    .fail(function() {
                        showToast('Có lỗi xảy ra khi xóa đánh giá', 'error');
                    });
            }
        }

        function showFullContent(danhGiaId) {
            // Get full content from server or from data attribute
            const row = $('#review-row-' + danhGiaId);
            const fullContent = row.data('full-content') || 'Đang tải nội dung...';

            $('#fullContentBody').html('<p>' + fullContent + '</p>');
            $('#fullContentModal').modal('show');
        }

        function exportWithFilters() {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);

            window.open('@Url.Action("Export")?' + params.toString(), '_blank');
        }

        function showToast(message, type) {
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const toast = $(`
                <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }

            $('#toast-container').append(toast);
            toast.toast('show');

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}
