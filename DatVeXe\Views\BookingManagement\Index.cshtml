@model List<DatVeXe.Models.ChuyenXe>
@{
    ViewData["Title"] = "Quản lý đơn đặt vé";
    var today = DateTime.Now.Date;
    var endDate = today.AddDays(7);
    var ngayKhoiHanh = ViewBag.NgayKhoiHanh;
    var trangThai = ViewBag.TrangThai ?? "all";
}

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="Admin" asp-action="Index">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">@ViewData["Title"]</li>
                </ol>
            </nav>
            <h2 class="mb-0"><i class="bi bi-ticket-detailed me-2"></i>@ViewData["Title"]</h2>
        </div>
    </div>

    <!-- Thống kê nhanh và bộ lọc -->
    <div class="row mb-4 g-3 align-items-stretch">
        <div class="col-lg-8">
            <div class="row g-3">
                <!-- Dashboard Cards -->
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-primary bg-opacity-10 text-primary rounded-circle me-3">
                                    <i class="bi bi-calendar-check fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Tổng chuyến xe</div>
                                    <h3 class="mb-0">@Model.Count</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-success bg-opacity-10 text-success rounded-circle me-3">
                                    <i class="bi bi-ticket fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Tổng vé đã đặt</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-info bg-opacity-10 text-info rounded-circle me-3">
                                    <i class="bi bi-check-circle fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Vé đã hoàn thành</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card admin-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-danger bg-opacity-10 text-danger rounded-circle me-3">
                                    <i class="bi bi-x-circle fs-4"></i>
                                </div>
                                <div>
                                    <div class="text-muted small fw-medium">Vé đã hủy</div>
                                    <h3 class="mb-0">@Model.Sum(c => c.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0)</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <form method="get" asp-action="Index" class="card admin-card h-100">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Lọc đơn đặt vé</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Ngày khởi hành</label>
                            <input type="date" name="ngayKhoiHanh" class="form-control" value="@(ngayKhoiHanh?.ToString("yyyy-MM-dd") ?? "")" />
                        </div>
                        <div class="col-12">
                            <label class="form-label">Trạng thái</label>
                            <select name="trangThai" class="form-select">
                                <option value="all" selected="@(trangThai == "all")">Tất cả</option>
                                <option value="chua_khoi_hanh" selected="@(trangThai == "chua_khoi_hanh")">Sắp khởi hành</option>
                                <option value="da_khoi_hanh" selected="@(trangThai == "da_khoi_hanh")">Đã khởi hành</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-admin-primary w-100">
                                <i class="bi bi-search me-2"></i>Lọc dữ liệu
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Danh sách chuyến xe -->
    <div class="card admin-card">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>Danh sách chuyến xe (@Model.Count)</h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary" id="btnRefresh">
                    <i class="bi bi-arrow-clockwise me-1"></i>Làm mới
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Count > 0)
            {
                <div class="table-responsive">
                    <table class="table table-hover align-middle admin-table mb-0">
                        <thead>
                            <tr>
                                <th>Tuyến đường</th>
                                <th>Thời gian</th>
                                <th>Xe & Tài xế</th>
                                <th>Trạng thái ghế</th>
                                <th>Thống kê vé</th>
                                <th width="120px" class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var chuyenXe in Model.OrderBy(c => c.NgayKhoiHanh))
                            {
                                var totalSeats = chuyenXe.Xe?.SoGhe ?? 0;
                                var bookedSeats = chuyenXe.Ves?.Count ?? 0;
                                var percentBooked = totalSeats > 0 ? (int)((double)bookedSeats / totalSeats * 100) : 0;
                                var isUpcoming = chuyenXe.NgayKhoiHanh > DateTime.Now;
                                var statusClass = isUpcoming ? "bg-success" : "bg-secondary";
                                var statusText = isUpcoming ? "Sắp khởi hành" : "Đã khởi hành";
                                
                                if (chuyenXe.TrangThai == false)
                                {
                                    statusClass = "bg-danger";
                                    statusText = "Đã hủy";
                                }
                                
                                var bookedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat || v.TrangThai == TrangThaiVe.DaThanhToan) ?? 0;
                                var usedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaSuDung) ?? 0;
                                var completedTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh) ?? 0;
                                var cancelledTickets = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0;

                                <tr>
                                    <td>
                                        <div class="fw-semibold">@chuyenXe.DiemDiDisplay → @chuyenXe.DiemDenDisplay</div>
                                        <div class="text-muted small">@chuyenXe.TuyenDuong?.KhoangCach km</div>
                                        <div><span class="badge @statusClass">@statusText</span></div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                        <div class="text-muted small">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                        @{
                                            var daysUntil = (chuyenXe.NgayKhoiHanh.Date - DateTime.Now.Date).TotalDays;
                                            if (daysUntil >= 0 && daysUntil <= 3 && isUpcoming)
                                            {
                                                <div class="text-danger small">
                                                    <i class="bi bi-exclamation-circle"></i>
                                                    @(daysUntil == 0 ? "Hôm nay" : $"Còn {daysUntil} ngày")
                                                </div>
                                            }
                                        }
                                    </td>
                                    <td>
                                        <div class="fw-medium">@chuyenXe.Xe?.BienSo</div>
                                        <div class="text-muted small">@chuyenXe.Xe?.LoaiXe - @chuyenXe.Xe?.SoGhe chỗ</div>
                                        <div class="text-muted small">
                                            @if (chuyenXe.TaiXe != null)
                                            {
                                                <i class="bi bi-person me-1"></i>@chuyenXe.TaiXe.HoTen
                                            }
                                            else
                                            {
                                                <span class="text-warning"><i class="bi bi-exclamation-triangle me-1"></i>Chưa phân công</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-grow-1 me-2">
                                                <div class="progress" style="height: 10px;">
                                                    <div class="progress-bar @(percentBooked > 80 ? "bg-danger" : percentBooked > 50 ? "bg-warning" : "bg-success")" 
                                                         role="progressbar" 
                                                         style="width: @percentBooked%" 
                                                         aria-valuenow="@percentBooked" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="text-muted small mt-1">@bookedSeats/@totalSeats chỗ (@percentBooked%)</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-2">
                                            @if (bookedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-primary">@bookedTickets đặt</span>
                                            }
                                            @if (usedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-success">@usedTickets đón</span>
                                            }
                                            @if (completedTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-info">@completedTickets hoàn thành</span>
                                            }
                                            @if (cancelledTickets > 0)
                                            {
                                                <span class="badge admin-badge bg-danger">@cancelledTickets hủy</span>
                                            }
                                            @if (bookedTickets == 0 && usedTickets == 0 && completedTickets == 0 && cancelledTickets == 0)
                                            {
                                                <span class="badge admin-badge bg-secondary">Chưa có vé</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 justify-content-center">
                                            <a asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @if (bookedSeats > 0)
                                            {
                                                <a asp-action="ExportPassengerList" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm btn-outline-success" title="Xuất DS hành khách">
                                                    <i class="bi bi-file-earmark-excel"></i>
                                                </a>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-bus-front display-1 text-muted"></i>
                    <p class="mt-3 text-muted">Không tìm thấy chuyến xe nào trong khoảng thời gian đã chọn</p>
                    <a asp-action="Index" class="btn btn-outline-primary mt-2">Xem tất cả chuyến xe</a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking-management').addClass('active');
            
            // Làm mới trang
            $('#btnRefresh').click(function() {
                location.reload();
            });
        });
    </script>
}
