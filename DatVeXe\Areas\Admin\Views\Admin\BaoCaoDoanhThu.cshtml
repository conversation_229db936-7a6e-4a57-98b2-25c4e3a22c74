@model IEnumerable<dynamic>

@{
    ViewData["Title"] = "Báo cáo doanh thu";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-chart-line" style="color: #9b59b6;"></i>
        Báo cáo doanh thu
    </h3>
    <div class="btn-group">
        <button class="btn" style="border: 1px solid #27ae60; color: #27ae60;">
            <i class="fas fa-download"></i>
            Xuất Excel
        </button>
        <button class="btn" style="border: 1px solid #7f8c8d; color: #7f8c8d;">
            <i class="fas fa-print"></i>
            In báo cáo
        </button>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center" style="border-left: 5px solid #9b59b6;">
            <div class="card-body">
                <h5 class="card-title" style="color: #9b59b6; font-weight: 600;">Tổng doanh thu</h5>
                <h3 style="color: #2c3e50; font-weight: bold;">@((Model?.Sum(x => x.DoanhThu) ?? 0).ToString("N0")) VNĐ</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="border-left: 5px solid #27ae60;">
            <div class="card-body">
                <h5 class="card-title" style="color: #27ae60; font-weight: 600;">Tổng số vé</h5>
                <h3 style="color: #2c3e50; font-weight: bold;">@(Model?.Sum(x => x.SoVe) ?? 0)</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="border-left: 5px solid #f39c12;">
            <div class="card-body">
                <h5 class="card-title" style="color: #f39c12; font-weight: 600;">Doanh thu TB/tháng</h5>
                <h3 style="color: #2c3e50; font-weight: bold;">@(Model?.Any() == true ? (Model.Sum(x => x.DoanhThu) / Model.Count()).ToString("N0") : "0") VNĐ</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="border-left: 5px solid #3498db;">
            <div class="card-body">
                <h5 class="card-title" style="color: #3498db; font-weight: 600;">Số tháng hoạt động</h5>
                <h3 style="color: #2c3e50; font-weight: bold;">@(Model?.Count() ?? 0)</h3>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table"></i>
            Chi tiết doanh thu theo tháng
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>Tháng/Năm</th>
                        <th>Số vé bán</th>
                        <th>Doanh thu</th>
                        <th>Doanh thu TB/vé</th>
                        <th>Tăng trưởng</th>
                    </tr>
                </thead>
                <tbody>
                    @{
                        var previousRevenue = 0m;
                    }
                    @if (Model != null)
                    {
                        @foreach (var item in Model)
                        {
                            var growthRate = previousRevenue > 0 ? ((item.DoanhThu - previousRevenue) / previousRevenue * 100) : 0;
                            <tr>
                                <td>@item.Thang/@item.Nam</td>
                                <td>@item.SoVe</td>
                                <td>@item.DoanhThu.ToString("N0") VNĐ</td>
                                <td>@(item.SoVe > 0 ? ((decimal)item.DoanhThu / item.SoVe).ToString("N0") : "0") VNĐ</td>
                                <td>
                                    @if (previousRevenue > 0)
                                    {
                                        if (growthRate > 0)
                                        {
                                            <span class="badge" style="background-color: #27ae60; color: white;">+@growthRate.ToString("F1")%</span>
                                        }
                                        else if (growthRate < 0)
                                        {
                                            <span class="badge" style="background-color: #e74c3c; color: white;">@growthRate.ToString("F1")%</span>
                                        }
                                        else
                                        {
                                            <span class="badge" style="background-color: #95a5a6; color: white;">0%</span>
                                        }
                                    }
                                    else
                                    {
                                        <span class="badge" style="background-color: #95a5a6; color: white;">-</span>
                                    }
                                </td>
                            </tr>
                            previousRevenue = item.DoanhThu;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@if (Model == null || !Model.Any())
{
    <div class="alert alert-info mt-4">
        <h5 class="alert-heading">
            <i class="fas fa-info-circle"></i>
            Chưa có dữ liệu doanh thu
        </h5>
        <p>Hiện tại chưa có dữ liệu doanh thu để hiển thị. Dữ liệu sẽ được cập nhật khi có vé được thanh toán.</p>
    </div>
}
