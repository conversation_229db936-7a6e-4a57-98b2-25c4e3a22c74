@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Chi tiết chuyến xe";
}
<div class="container mt-4">
    <h3>Chi tiết chuyến xe</h3>
    <dl class="row">
        <dt class="col-sm-3">ID</dt>
        <dd class="col-sm-9">@Model.ChuyenXeId</dd>
        <dt class="col-sm-3">Xe</dt>
        <dd class="col-sm-9">@Model.Xe?.BienSo</dd>
        <dt class="col-sm-3">Tuyế<PERSON> đường</dt>
        <dd class="col-sm-9">@Model.TuyenDuong?.TenTuyen</dd>
        <dt class="col-sm-3">Ngày khởi hành</dt>
        <dd class="col-sm-9">@Model.NgayKhoiHanh.ToString("dd/MM/yyyy")</dd>
        <dt class="col-sm-3">Thờ<PERSON> gian đi</dt>
        <dd class="col-sm-9">@Model.ThoiGianDi.ToString(@"hh\:mm")</dd>
        <dt class="col-sm-3">Giá vé</dt>
        <dd class="col-sm-9">@Model.Gia.ToString("N0") VNĐ</dd>
        <dt class="col-sm-3">Trạng thái</dt>
        <dd class="col-sm-9">@Model.TrangThaiChuyenXe.ToString()</dd>
    </dl>
    <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Index" class="btn btn-secondary">Quay lại</a>
</div>
