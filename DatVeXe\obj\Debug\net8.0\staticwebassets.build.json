{"Version": 1, "Hash": "QfeMFk+m3fqxFAGB96xZqW+6N42ftjBYdQ9bErq2cpM=", "Source": "DatVeXe", "BasePath": "_content/DatVeXe", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DatVeXe\\wwwroot", "Source": "DatVeXe", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DatVeXe.styles.css", "SourceId": "DatVeXe", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/DatVeXe", "RelativePath": "DatVeXe#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "kcm6oz8pcj", "Integrity": "pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DatVeXe.styles.css", "FileLength": 1125, "LastWriteTime": "2025-06-15T13:21:43+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DatVeXe.bundle.scp.css", "SourceId": "DatVeXe", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/DatVeXe", "RelativePath": "DatVeXe#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "kcm6oz8pcj", "Integrity": "pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DatVeXe.bundle.scp.css", "FileLength": 1125, "LastWriteTime": "2025-06-15T13:21:43+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\admin.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/admin#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a5qpn6nwqe", "Integrity": "YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\admin.css", "FileLength": 10555, "LastWriteTime": "2025-06-17T14:00:43+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\color-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/color-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "avs8i5688v", "Integrity": "2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\color-fixes.css", "FileLength": 9464, "LastWriteTime": "2025-06-12T03:27:05+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\page-specific-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/page-specific-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s6so21365t", "Integrity": "tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\page-specific-fixes.css", "FileLength": 9930, "LastWriteTime": "2025-06-12T03:28:05+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\site.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x3u8v4r6s1", "Integrity": "bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 14625, "LastWriteTime": "2025-06-12T03:28:55+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\BusBanner.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/BusBanner#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wswepo8j2e", "Integrity": "cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\BusBanner.jpg", "FileLength": 412962, "LastWriteTime": "2025-05-29T16:53:47+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancity.webp", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancity#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cuj6g20mhu", "Integrity": "DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancity.webp", "FileLength": 518686, "LastWriteTime": "2025-05-30T18:28:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancitybus.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancitybus#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p3yf6yxrqx", "Integrity": "m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancitybus.jpg", "FileLength": 162920, "LastWriteTime": "2025-05-30T18:33:06+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\readmarid.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/readmarid#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qmrga2uz31", "Integrity": "Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\readmarid.jpg", "FileLength": 114373, "LastWriteTime": "2025-06-06T04:00:01+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\realvscity.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/realvscity#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1ydqed4cvh", "Integrity": "59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\realvscity.jpg", "FileLength": 56258, "LastWriteTime": "2025-06-12T03:17:05+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\admin.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/admin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "etsnhtcic7", "Integrity": "XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\admin.js", "FileLength": 13289, "LastWriteTime": "2025-06-16T18:19:07+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\color-contrast-checker.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/color-contrast-checker#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9qy6rw296h", "Integrity": "UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\color-contrast-checker.js", "FileLength": 11054, "LastWriteTime": "2025-06-12T03:29:44+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\site.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\user-management.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/user-management#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z797e9e25e", "Integrity": "bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-management.js", "FileLength": 7153, "LastWriteTime": "2025-06-15T13:31:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\test-routes.html", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "test-routes#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nne9mve8jp", "Integrity": "c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-routes.html", "FileLength": 440, "LastWriteTime": "2025-06-15T13:31:56+00:00"}], "Endpoints": [{"Route": "css/admin.a5qpn6nwqe.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\admin.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10555"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 14:00:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a5qpn6nwqe"}, {"Name": "label", "Value": "css/admin.css"}, {"Name": "integrity", "Value": "sha256-YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY="}]}, {"Route": "css/admin.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\admin.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10555"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 14:00:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY="}]}, {"Route": "css/color-fixes.avs8i5688v.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\color-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9464"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:27:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "avs8i5688v"}, {"Name": "label", "Value": "css/color-fixes.css"}, {"Name": "integrity", "Value": "sha256-2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY="}]}, {"Route": "css/color-fixes.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\color-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9464"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:27:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY="}]}, {"Route": "css/page-specific-fixes.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\page-specific-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9930"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:28:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA="}]}, {"Route": "css/page-specific-fixes.s6so21365t.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\page-specific-fixes.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9930"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:28:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s6so21365t"}, {"Name": "label", "Value": "css/page-specific-fixes.css"}, {"Name": "integrity", "Value": "sha256-tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA="}]}, {"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14625"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:28:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c="}]}, {"Route": "css/site.x3u8v4r6s1.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14625"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:28:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x3u8v4r6s1"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c="}]}, {"Route": "DatVeXe.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DatVeXe.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1125"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:21:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8="}]}, {"Route": "DatVeXe.kcm6oz8pcj.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DatVeXe.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1125"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:21:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kcm6oz8pcj"}, {"Name": "label", "Value": "DatVeXe.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8="}]}, {"Route": "DatVeXe.kcm6oz8pcj.styles.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DatVeXe.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1125"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:21:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kcm6oz8pcj"}, {"Name": "label", "Value": "DatVeXe.styles.css"}, {"Name": "integrity", "Value": "sha256-pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8="}]}, {"Route": "DatVeXe.styles.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DatVeXe.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1125"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:21:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pPrEbrmoiC0a5v7v9ptz2ps+8M7LlgZLXk6/js7y+N8="}]}, {"Route": "images/BusBanner.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\BusBanner.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "412962"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:53:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk="}]}, {"Route": "images/BusBanner.wswepo8j2e.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\BusBanner.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "412962"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:53:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wswepo8j2e"}, {"Name": "label", "Value": "images/BusBanner.jpg"}, {"Name": "integrity", "Value": "sha256-cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk="}]}, {"Route": "images/mancity.cuj6g20mhu.webp", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancity.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "518686"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 18:28:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cuj6g20mhu"}, {"Name": "label", "Value": "images/mancity.webp"}, {"Name": "integrity", "Value": "sha256-DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk="}]}, {"Route": "images/mancity.webp", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancity.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "518686"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 18:28:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk="}]}, {"Route": "images/mancitybus.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancitybus.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162920"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 18:33:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA="}]}, {"Route": "images/mancitybus.p3yf6yxrqx.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancitybus.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162920"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 18:33:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p3yf6yxrqx"}, {"Name": "label", "Value": "images/mancitybus.jpg"}, {"Name": "integrity", "Value": "sha256-m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA="}]}, {"Route": "images/readmarid.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\readmarid.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114373"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8=\""}, {"Name": "Last-Modified", "Value": "Fri, 06 Jun 2025 04:00:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8="}]}, {"Route": "images/readmarid.qmrga2uz31.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\readmarid.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114373"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8=\""}, {"Name": "Last-Modified", "Value": "Fri, 06 Jun 2025 04:00:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qmrga2uz31"}, {"Name": "label", "Value": "images/readmarid.jpg"}, {"Name": "integrity", "Value": "sha256-Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8="}]}, {"Route": "images/realvscity.1ydqed4cvh.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\realvscity.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56258"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:17:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1ydqed4cvh"}, {"Name": "label", "Value": "images/realvscity.jpg"}, {"Name": "integrity", "Value": "sha256-59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI="}]}, {"Route": "images/realvscity.jpg", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\realvscity.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56258"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:17:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI="}]}, {"Route": "js/admin.etsnhtcic7.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\admin.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 18:19:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etsnhtcic7"}, {"Name": "label", "Value": "js/admin.js"}, {"Name": "integrity", "Value": "sha256-XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM="}]}, {"Route": "js/admin.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\admin.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13289"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 18:19:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM="}]}, {"Route": "js/color-contrast-checker.9qy6rw296h.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\color-contrast-checker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11054"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:29:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9qy6rw296h"}, {"Name": "label", "Value": "js/color-contrast-checker.js"}, {"Name": "integrity", "Value": "sha256-UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ="}]}, {"Route": "js/color-contrast-checker.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\color-contrast-checker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11054"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 03:29:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ="}]}, {"Route": "js/site.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/user-management.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\user-management.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7153"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:31:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao="}]}, {"Route": "js/user-management.z797e9e25e.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\user-management.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7153"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z797e9e25e"}, {"Name": "label", "Value": "js/user-management.js"}, {"Name": "integrity", "Value": "sha256-bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.agp80tu62r.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "agp80tu62r"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.st1cbwfwo5.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "st1cbwfwo5"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.5vj65cig9w.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vj65cig9w"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.unj9p35syc.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "unj9p35syc"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.2q4vfeazbq.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2q4vfeazbq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.o371a8zbv2.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o371a8zbv2"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.n1oizzvkh6.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n1oizzvkh6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.q2ku51ktnl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q2ku51ktnl"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.7na4sro3qu.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7na4sro3qu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.jeal3x0ldm.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jeal3x0ldm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.okkk44j0xs.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okkk44j0xs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.f8imaxxbri.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8imaxxbri"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.0wve5yxp74.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0wve5yxp74"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.cwzlr5n8x4.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwzlr5n8x4"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.wmug9u23qg.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wmug9u23qg"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.npxfuf8dg6.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npxfuf8dg6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.j75batdsum.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j75<PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.16095smhkz.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16095smhkz"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.vy0bq9ydhf.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vy0bq9ydhf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.b4skse8du6.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b4skse8du6"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.ab1c3rmv7g.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab1c3rmv7g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.56d2bn4wt9.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56d2bn4wt9"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.u3xrusw2ol.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3xrusw2ol"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.tey0rigmnh.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tey0rigmnh"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.73kdqttayv.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "73kdqttayv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.mpyigms19s.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mpyigms19s"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.4gxs3k148c.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4gxs3k148c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.9b9oa1qrmt.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9b9oa1qrmt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.fctod5rc9n.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fctod5rc9n"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ve6x09088i.css", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ve6x09088i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.kbynt5jhd9.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbynt5jhd9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.l2av4jpuoj.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l2av4jpuoj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.25iw1kog22.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25iw1kog22"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.c2nslu3uf3.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2nslu3uf3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.2lgwfvgpvi.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2lgwfvgpvi"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.m39kt2b5c9.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m39kt2b5c9"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.wsezl0heh6.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wsezl0heh6"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.um2aeqy4ik.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "um2aeqy4ik"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.6ukhryfubh.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6<PERSON><PERSON><PERSON><PERSON>bh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.u33ctipx7g.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u33ctipx7g"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zwph15dxgs.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zwph15dxgs"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.o4kw7cc6tf.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4kw7cc6tf"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.ay5nd8zt9x.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5nd8zt9x"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.9oaff4kq20.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9oaff4kq20"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery/dist/jquery.fwhahm2icz.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fwhahm2icz"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.min.5pze98is44.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5pze98is44"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "lib/jquery/dist/jquery.min.dd6z7egasc.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dd6z7egasc"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 16:25:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "test-routes.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\test-routes.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "440"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:31:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk="}]}, {"Route": "test-routes.nne9mve8jp.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\test-routes.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "440"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 13:31:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nne9mve8jp"}, {"Name": "label", "Value": "test-routes.html"}, {"Name": "integrity", "Value": "sha256-c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk="}]}]}