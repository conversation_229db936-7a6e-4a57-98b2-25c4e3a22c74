using DatVeXe.Models;
using System.Text;
using System.Security.Cryptography;

namespace DatVeXe.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly ILogger<PaymentService> _logger;
        private readonly IConfiguration _configuration;

        public PaymentService(ILogger<PaymentService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<PaymentResponseViewModel> CreatePaymentAsync(PaymentRequestViewModel request)
        {
            try
            {
                _logger.LogInformation($"Creating payment for transaction: {request.VeId}");

                switch (request.PhuongThuc)
                {
                    case PhuongThucThanhToan.VNPay:
                        return await CreateVNPayPaymentAsync(request);
                    
                    case PhuongThucThanhToan.MoMo:
                        return await CreateMoMoPaymentAsync(request);
                    
                    case PhuongThucThanhToan.ZaloPay:
                        return await CreateZaloPayPaymentAsync(request);
                    
                    case PhuongThucThanhToan.ChuyenKhoan:
                        return await CreateBankTransferAsync(request);
                    
                    case PhuongThucThanhToan.TaiQuay:
                        return new PaymentResponseViewModel
                        {
                            Success = true,
                            Message = "Vé đã được đặt. Vui lòng thanh toán tại quầy.",
                            TrangThai = TrangThaiThanhToan.ChoThanhToan
                        };
                    
                    default:
                        return new PaymentResponseViewModel
                        {
                            Success = false,
                            Message = "Phương thức thanh toán không được hỗ trợ",
                            TrangThai = TrangThaiThanhToan.ThatBai
                        };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment");
                return new PaymentResponseViewModel
                {
                    Success = false,
                    Message = "Có lỗi xảy ra khi tạo thanh toán",
                    TrangThai = TrangThaiThanhToan.ThatBai
                };
            }
        }

        public async Task<PaymentResponseViewModel> ProcessPaymentCallbackAsync(string transactionId, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation($"Processing payment callback for transaction: {transactionId}");

                // Demo mode - simulate successful payment
                await Task.Delay(100);

                return new PaymentResponseViewModel
                {
                    Success = true,
                    Message = "Thanh toán thành công",
                    MaGiaoDich = transactionId,
                    TrangThai = TrangThaiThanhToan.ThanhCong,
                    ThoiGianThanhToan = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing payment callback for transaction: {transactionId}");
                return new PaymentResponseViewModel
                {
                    Success = false,
                    Message = "Lỗi xử lý callback thanh toán",
                    TrangThai = TrangThaiThanhToan.ThatBai
                };
            }
        }

        public async Task<PaymentResponseViewModel> CheckPaymentStatusAsync(string transactionId)
        {
            try
            {
                _logger.LogInformation($"Checking payment status for transaction: {transactionId}");

                // Demo mode - simulate payment status check
                await Task.Delay(100);

                return new PaymentResponseViewModel
                {
                    Success = true,
                    Message = "Trạng thái thanh toán đã được cập nhật",
                    MaGiaoDich = transactionId,
                    TrangThai = TrangThaiThanhToan.ThanhCong,
                    ThoiGianThanhToan = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking payment status for transaction: {transactionId}");
                return new PaymentResponseViewModel
                {
                    Success = false,
                    Message = "Lỗi kiểm tra trạng thái thanh toán",
                    TrangThai = TrangThaiThanhToan.ThatBai
                };
            }
        }

        public async Task<bool> RefundPaymentAsync(string transactionId, decimal amount, string reason)
        {
            try
            {
                _logger.LogInformation($"Processing refund for transaction: {transactionId}, amount: {amount}");

                // Demo mode - simulate refund
                await Task.Delay(100);

                _logger.LogInformation($"Refund processed successfully for transaction: {transactionId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing refund for transaction: {transactionId}");
                return false;
            }
        }

        public string GeneratePaymentUrl(PhuongThucThanhToan method, string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            switch (method)
            {
                case PhuongThucThanhToan.VNPay:
                    return GenerateVNPayUrl(transactionId, amount, orderInfo, returnUrl);
                
                case PhuongThucThanhToan.MoMo:
                    return GenerateMoMoUrl(transactionId, amount, orderInfo, returnUrl);
                
                case PhuongThucThanhToan.ZaloPay:
                    return GenerateZaloPayUrl(transactionId, amount, orderInfo, returnUrl);
                
                default:
                    return returnUrl;
            }
        }

        private async Task<PaymentResponseViewModel> CreateVNPayPaymentAsync(PaymentRequestViewModel request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=VNPay&transactionId={request.VeId}&amount={request.SoTien}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            return new PaymentResponseViewModel
            {
                Success = true,
                Message = "Chuyển hướng đến VNPay Demo",
                PaymentUrl = paymentUrl,
                TrangThai = TrangThaiThanhToan.DangXuLy
            };
        }

        private async Task<PaymentResponseViewModel> CreateMoMoPaymentAsync(PaymentRequestViewModel request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=MoMo&transactionId={request.VeId}&amount={request.SoTien}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            return new PaymentResponseViewModel
            {
                Success = true,
                Message = "Chuyển hướng đến MoMo Demo",
                PaymentUrl = paymentUrl,
                TrangThai = TrangThaiThanhToan.DangXuLy
            };
        }

        private async Task<PaymentResponseViewModel> CreateZaloPayPaymentAsync(PaymentRequestViewModel request)
        {
            // Demo implementation - redirect to demo payment page
            var baseUrl = GetBaseUrl();
            var paymentUrl = $"{baseUrl}/Booking/DemoPayment?method=ZaloPay&transactionId={request.VeId}&amount={request.SoTien}&orderInfo={Uri.EscapeDataString(request.OrderInfo ?? "")}&returnUrl={Uri.EscapeDataString(request.ReturnUrl ?? "")}&cancelUrl={Uri.EscapeDataString(request.CancelUrl ?? "")}";

            return new PaymentResponseViewModel
            {
                Success = true,
                Message = "Chuyển hướng đến ZaloPay Demo",
                PaymentUrl = paymentUrl,
                TrangThai = TrangThaiThanhToan.DangXuLy
            };
        }

        private string GetBaseUrl()
        {
            // In a real application, this should be injected or configured
            return "http://localhost:5057";
        }

        private async Task<PaymentResponseViewModel> CreateBankTransferAsync(PaymentRequestViewModel request)
        {
            // For bank transfer, provide bank account information
            return new PaymentResponseViewModel
            {
                Success = true,
                Message = "Vui lòng chuyển khoản theo thông tin được cung cấp",
                TrangThai = TrangThaiThanhToan.ChoThanhToan
            };
        }

        private string GenerateVNPayUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?vnp_ResponseCode=00&vnp_TxnRef={transactionId}&vnp_Amount={amount * 100}&vnp_TransactionStatus=00&demo=true";
        }

        private string GenerateMoMoUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?resultCode=0&orderId={transactionId}&amount={amount}&message=Success&demo=true";
        }

        private string GenerateZaloPayUrl(string transactionId, decimal amount, string orderInfo, string returnUrl)
        {
            // Demo mode - simulate payment gateway by redirecting to callback with success parameters
            var encodedReturnUrl = Uri.EscapeDataString(returnUrl);
            return $"{returnUrl}?status=1&app_trans_id={transactionId}&amount={amount}&demo=true";
        }
    }
}
