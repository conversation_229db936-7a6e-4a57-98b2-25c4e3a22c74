using DatVeXe.Models;

namespace DatVeXe.Services
{
    public interface IPaymentService
    {
        Task<PaymentResponseViewModel> CreatePaymentAsync(PaymentRequestViewModel request);
        Task<PaymentResponseViewModel> ProcessPaymentCallbackAsync(string transactionId, Dictionary<string, string> parameters);
        Task<PaymentResponseViewModel> CheckPaymentStatusAsync(string transactionId);
        Task<bool> RefundPaymentAsync(string transactionId, decimal amount, string reason);
        string GeneratePaymentUrl(PhuongThucThanhToan method, string transactionId, decimal amount, string orderInfo, string returnUrl);
    }
}
