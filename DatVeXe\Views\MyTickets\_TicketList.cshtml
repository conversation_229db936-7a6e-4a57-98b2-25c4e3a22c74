@model IEnumerable<Ve>

@if (Model.Any())
{
    <div class="row">
        @foreach (var ticket in Model)
        {
            var trangThaiClass = ticket.TrangThai switch
            {
                TrangThaiVe.DaDat => "border-primary",
                TrangThaiVe.DaThanhToan => "border-success",
                TrangThaiVe.DaHoanThanh => "border-info",
                TrangThaiVe.DaHuy => "border-danger",
                _ => "border-secondary"
            };

            var trangThaiBadge = ticket.TrangThai switch
            {
                TrangThaiVe.DaDat => "bg-primary",
                TrangThaiVe.DaThanhToan => "bg-success",
                TrangThaiVe.DaHoanThanh => "bg-info",
                TrangThaiVe.DaHuy => "bg-danger",
                _ => "bg-secondary"
            };

            var trangThaiText = ticket.TrangThai switch
            {
                TrangThaiVe.DaDat => "Đã đặt",
                TrangThaiVe.DaThanhToan => "Đã thanh toán",
                TrangThaiVe.DaHoanThanh => "Đã hoàn thành",
                TrangThaiVe.DaHuy => "Đã hủy",
                _ => "Không xác định"
            };

            var timeRemaining = ticket.ChuyenXe != null && ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now ? 
                ticket.ChuyenXe.NgayKhoiHanh - DateTime.Now : TimeSpan.Zero;

            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card border-0 shadow-sm h-100 @trangThaiClass" style="border-left: 4px solid !important;">
                    <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge @trangThaiBadge">@trangThaiText</span>
                            @if (ticket.TrangThai == TrangThaiVe.DaDat && timeRemaining.TotalHours > 0 && timeRemaining.TotalDays <= 1)
                            {
                                <span class="badge bg-warning text-dark ms-2">
                                    <i class="bi bi-clock me-1"></i>
                                    @if (timeRemaining.TotalHours < 24)
                                    {
                                        @($"{timeRemaining.Hours}h {timeRemaining.Minutes}m")
                                    }
                                    else
                                    {
                                        @($"{timeRemaining.Days} ngày")
                                    }
                                </span>
                            }
                        </div>
                        <small class="text-muted">@ticket.MaVe</small>
                    </div>
                    
                    <div class="card-body">
                        <!-- Thông tin chuyến xe -->
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">@ticket.ChuyenXe?.DiemDi → @ticket.ChuyenXe?.DiemDen</div>
                                    <small class="text-muted">@ticket.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</small>
                                </div>
                            </div>
                        </div>

                        <!-- Thông tin hành khách -->
                        <div class="mb-3">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-fill text-info me-2"></i>
                                <div>
                                    <div class="fw-semibold">@ticket.TenKhach</div>
                                    <small class="text-muted">@ticket.SoDienThoai</small>
                                </div>
                            </div>
                        </div>

                        <!-- Thông tin xe và ghế -->
                        <div class="mb-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted d-block">Nhà xe</small>
                                    <span class="fw-semibold">@ticket.ChuyenXe?.Xe?.NhaXe</span>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">Ghế số</small>
                                    <span class="fw-semibold">@ticket.ChoNgoi?.SoGhe</span>
                                </div>
                            </div>
                        </div>

                        <!-- Giá vé -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">Giá vé:</span>
                                <span class="fw-bold text-success fs-5">@string.Format("{0:N0}", ticket.GiaVe) VNĐ</span>
                            </div>
                        </div>

                        <!-- Ngày đặt -->
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-calendar3 me-1"></i>
                                Đặt lúc: @ticket.NgayDat.ToString("dd/MM/yyyy HH:mm")
                            </small>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent">
                        <div class="d-flex gap-2">
                            <a asp-action="Details" asp-route-id="@ticket.VeId" class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="bi bi-eye me-1"></i>Chi tiết
                            </a>

                            @if (ticket.TrangThai == TrangThaiVe.DaDat && ticket.ChuyenXe != null && ticket.ChuyenXe.NgayKhoiHanh > DateTime.Now.AddHours(2))
                            {
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        data-bs-toggle="modal" data-bs-target="#cancelModal" 
                                        data-ticket-id="@ticket.VeId" data-ticket-code="@ticket.MaVe">
                                    <i class="bi bi-x-circle me-1"></i>Hủy
                                </button>
                            }

                            @if (ticket.TrangThai == TrangThaiVe.DaHoanThanh)
                            {
                                <a asp-controller="Review" asp-action="Create" asp-route-id="@ticket.VeId"
                                   class="btn btn-outline-warning btn-sm">
                                    <i class="bi bi-star me-1"></i>Đánh giá
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="bi bi-ticket-perforated display-1 text-muted"></i>
        </div>
        <h4 class="text-muted">Chưa có vé nào</h4>
        <p class="text-muted">Bạn chưa đặt vé nào. Hãy đặt vé đầu tiên của bạn!</p>
        <a asp-controller="Booking" asp-action="Search" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Đặt vé ngay
        </a>
    </div>
}

<!-- Modal hủy vé -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận hủy vé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="cancelForm" method="post">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Bạn có chắc chắn muốn hủy vé <strong id="ticketCode"></strong>?
                        <br><small>Tiền sẽ được hoàn lại trong 3-5 ngày làm việc.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="lyDoHuy" class="form-label">Lý do hủy (tùy chọn)</label>
                        <textarea name="lyDoHuy" id="lyDoHuy" class="form-control" rows="3" 
                                  placeholder="Nhập lý do hủy vé..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-x-circle me-2"></i>Xác nhận hủy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Handle cancel modal
    $('#cancelModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var ticketId = button.data('ticket-id');
        var ticketCode = button.data('ticket-code');
        
        var modal = $(this);
        modal.find('#ticketCode').text(ticketCode);
        modal.find('#cancelForm').attr('action', '@Url.Action("Cancel", "MyTickets")/' + ticketId);
    });
</script>
